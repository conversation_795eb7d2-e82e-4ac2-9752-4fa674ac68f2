/**
 * Daily Questions Service
 *
 * Handles daily couple bonding questions, responses, and partner interactions.
 * Provides question delivery, response management, and engagement tracking.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { supabase } from '../../shared/services/supabase/client';
import { logger } from '../../shared/utils/logger';
import { logEvent } from '../services/analytics/eventLogger';
import { STREAK_EVENT_CATEGORIES, STREAK_EVENT_TYPES, streakEventService } from './streakEventService';

// Type definitions for daily questions
export interface DailyQuestion {
  id: string;
  question_id: string;
  question_text: string;
  category: string;
  tone?: string;
  difficulty?: string;
  is_active: boolean;
  created_at: string;
}

export interface DailyQuestionResponse {
  id: string;
  user_id: string;
  couple_id: string;
  question_id: string;
  response_text: string;
  question_date: string;
  is_visible_to_partner: boolean;
  created_at: string;
  updated_at: string;
}

export interface DailyQuestionReaction {
  id: string;
  response_id: string;
  user_id: string;
  reaction_type: string;
  created_at: string;
}

export interface DailyQuestionComment {
  id: string;
  response_id: string;
  user_id: string;
  comment_text: string;
  created_at: string;
}

export interface CoupleResponseStatus {
  user_id: string;
  has_responded: boolean;
  response_text?: string;
  responded_at?: string;
}

export interface QuestionHistory {
  question_date: string;
  question: DailyQuestion;
  user_response?: DailyQuestionResponse;
  partner_response?: DailyQuestionResponse;
}

export interface StreakData {
  current_streak: number;
  longest_streak: number;
  last_activity_date?: string;
  total_responses: number;
}

class DailyQuestionsService {
  /**
   * Get today's question for a couple
   */
  async getTodaysQuestion(coupleId: string): Promise<DailyQuestion | null> {
    try {
      // Get today's date
      const today = new Date().toISOString().split('T')[0];

      const { data, error } = await (supabase as any)
        .from('daily_questions')
        .select('*')
        .eq('is_active', true)
        .limit(1);

      if (error) {
        logger.error('Failed to get today\'s question:', error);
        throw new Error(`Failed to get today's question: ${error.message}`);
      }

      if (!data || data.length === 0) {
        logger.warn('No question available for today');
        return null;
      }

      return data[0] as unknown as DailyQuestion;
    } catch (error) {
      logger.error('Error getting today\'s question:', error);
      throw error instanceof Error ? error : new Error('Failed to get today\'s question');
    }
  }

  /**
   * Get couple's response status for today
   */
  async getCoupleResponseStatus(coupleId: string, questionDate?: string): Promise<CoupleResponseStatus[]> {
    try {
      const date = questionDate || new Date().toISOString().split('T')[0];

      const { data, error } = await (supabase as any)
        .from('daily_question_responses')
        .select('*')
        .eq('couple_id', coupleId)
        .eq('question_date', date);

      if (error) {
        logger.error('Failed to get couple response status:', error);
        return [];
      }

      return (data || []) as unknown as CoupleResponseStatus[];
    } catch (error) {
      logger.error('Error getting couple response status:', error);
      return [];
    }
  }

  /**
   * Submit a response to today's question
   */
  async submitResponse(
    userId: string,
    coupleId: string,
    questionId: string,
    responseText: string,
    questionDate?: string
  ): Promise<DailyQuestionResponse | null> {
    try {
      const date = questionDate || new Date().toISOString().split('T')[0];

      // Check if user already answered today
      const existingResponse = await this.getUserResponse(userId, date);
      if (existingResponse) {
        logger.warn('User already answered today\'s question');
        return null;
      }

      const { data, error } = await (supabase as any)
        .from('daily_question_responses')
        .insert({
          user_id: userId,
          couple_id: coupleId,
          question_id: questionId,
          response_text: responseText,
          question_date: date,
          is_visible_to_partner: true
        })
        .select();

      if (error) {
        logger.error('Failed to submit response:', error);
        return null;
      }

      // Log analytics event
      await logEvent('daily_question_answered', {
        question_id: questionId,
        response_length: responseText.length,
        question_date: date
      });

      // Track streak event
      const streakTracked = await streakEventService.trackEvent({
        user_id: userId,
        couple_id: coupleId,
        event_name: 'Daily Question Answered',
        event_category: STREAK_EVENT_CATEGORIES.DAILY_QUESTIONS,
        event_type: STREAK_EVENT_TYPES.DAILY_QUESTION_ANSWERED,
        streak_eligible: true,
        points_awarded: 10,
        metadata: {
          question_id: questionId,
          response_length: responseText.length,
          question_date: date
        }
      });

      if (!streakTracked) {
        logger.warn('Failed to track streak event for daily question');
      }

      return (data && data.length > 0 ? data[0] : null) as unknown as DailyQuestionResponse;
    } catch (error) {
      logger.error('Error submitting response:', error);
      return null;
    }
  }

  /**
   * Get user's response for a specific date
   */
  async getUserResponse(userId: string, questionDate: string): Promise<DailyQuestionResponse | null> {
    try {
      const { data, error } = await (supabase as any)
        .from('daily_question_responses')
        .select('*')
        .eq('user_id', userId)
        .eq('question_date', questionDate)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        logger.error('Failed to get user response:', error);
        return null;
      }

      return data as unknown as DailyQuestionResponse | null;
    } catch (error) {
      logger.error('Error getting user response:', error);
      return null;
    }
  }

  /**
   * Update user's response (within edit window)
   */
  async updateResponse(
    responseId: string,
    responseText: string,
    userId: string
  ): Promise<DailyQuestionResponse | null> {
    try {
      // Check if response is within edit window (30 minutes)
      const { data: existingResponse, error: fetchError } = await (supabase as any)
        .from('daily_question_responses')
        .select('answered_at, user_id')
        .eq('id', responseId)
        .maybeSingle();

      if (fetchError) {
        logger.error('Failed to fetch existing response:', fetchError);
        return null;
      }

      if (!existingResponse || (existingResponse as any).user_id !== userId) {
        logger.error('User not authorized to update this response');
        return null;
      }

      const answeredAt = new Date((existingResponse as any).answered_at);
      const now = new Date();
      const editWindow = 30 * 60 * 1000; // 30 minutes in milliseconds

      if (now.getTime() - answeredAt.getTime() > editWindow) {
        logger.warn('Response edit window has expired');
        return null;
      }

      const { data, error } = await supabase
        .from('daily_question_responses')
        .update({ response_text: responseText })
        .eq('id', responseId)
        .select()
        .single();

      if (error) {
        logger.error('Failed to update response:', error);
        return null;
      }

      return data as DailyQuestionResponse;
    } catch (error) {
      logger.error('Error updating response:', error);
      return null;
    }
  }

  /**
   * Add a reaction to a response
   */
  async addReaction(
    responseId: string,
    userId: string,
    reactionType: 'heart' | 'laugh' | 'surprise' | 'love'
  ): Promise<DailyQuestionReaction | null> {
    try {
      const { data, error } = await supabase
        .from('daily_question_reactions')
        .upsert({
          response_id: responseId,
          user_id: userId,
          reaction_type: reactionType
        })
        .select()
        .single();

      if (error) {
        logger.error('Failed to add reaction:', error);
        return null;
      }

      // Log analytics event
      await logEvent('daily_question_reaction_added', {
        response_id: responseId,
        reaction_type: reactionType
      });

      return data as DailyQuestionReaction;
    } catch (error) {
      logger.error('Error adding reaction:', error);
      return null;
    }
  }

  /**
   * Add a comment to a response
   */
  async addComment(
    responseId: string,
    userId: string,
    commentText: string
  ): Promise<DailyQuestionComment | null> {
    try {
      const { data, error } = await supabase
        .from('daily_question_comments')
        .insert({
          response_id: responseId,
          user_id: userId,
          comment_text: commentText
        })
        .select()
        .single();

      if (error) {
        logger.error('Failed to add comment:', error);
        return null;
      }

      // Log analytics event
      await logEvent('daily_question_comment_added', {
        response_id: responseId,
        comment_length: commentText.length
      });

      return data as DailyQuestionComment;
    } catch (error) {
      logger.error('Error adding comment:', error);
      return null;
    }
  }

  /**
   * Get question history for a couple
   */
  async getQuestionHistory(
    coupleId: string,
    userId: string,
    limit: number = 30,
    offset: number = 0
  ): Promise<QuestionHistory[]> {
    try {
      const { data, error } = await supabase
        .from('daily_question_schedule')
        .select(`
          question_date,
          daily_questions!inner(
            question_id,
            question_text,
            category,
            tone,
            difficulty
          )
        `)
        .eq('is_active', true)
        .order('question_date', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error('Failed to get question history:', error);
        return [];
      }

      const history: QuestionHistory[] = [];

      for (const item of data) {
        const questionDate = item.question_date;

        // Get responses for this date
        const { data: responses } = await supabase
          .from('daily_question_responses')
          .select('*')
          .eq('couple_id', coupleId)
          .eq('question_date', questionDate);

        const userResponse = responses?.find(r => r.user_id === userId) || null;
        const partnerResponse = responses?.find(r => r.user_id !== userId) || null;

        // Get reactions and comments
        const responseIds = responses?.map(r => r.id) || [];

        const { data: reactions } = await supabase
          .from('daily_question_reactions')
          .select('*')
          .in('response_id', responseIds);

        const { data: comments } = await supabase
          .from('daily_question_comments')
          .select('*')
          .in('response_id', responseIds)
          .order('created_at', { ascending: true });

        history.push({
          question_date: questionDate,
          question: {
            question_id: item.daily_questions.question_id,
            question_text: item.daily_questions.question_text,
            category: item.daily_questions.category,
            tone: item.daily_questions.tone,
            difficulty: item.daily_questions.difficulty,
            question_date: questionDate
          },
          user_response: userResponse,
          partner_response: partnerResponse,
          user_reactions: reactions?.filter(r => r.user_id === userId) || [],
          partner_reactions: reactions?.filter(r => r.user_id !== userId) || [],
          comments: comments || []
        });
      }

      return history;
    } catch (error) {
      logger.error('Error getting question history:', error);
      return [];
    }
  }

  /**
   * Get user's streak data
   */
  async getStreakData(userId: string): Promise<StreakData> {
    try {
      // Get all user responses ordered by date
      const { data: responses, error } = await supabase
        .from('daily_question_responses')
        .select('question_date')
        .eq('user_id', userId)
        .order('question_date', { ascending: false });

      if (error) {
        logger.error('Failed to get streak data:', error);
        return {
          current_streak: 0,
          longest_streak: 0,
          total_questions_answered: 0,
          last_answered_date: null
        };
      }

      if (!responses || responses.length === 0) {
        return {
          current_streak: 0,
          longest_streak: 0,
          total_questions_answered: 0,
          last_answered_date: null
        };
      }

      const dates = responses.map(r => new Date(r.question_date));
      const totalAnswered = responses.length;
      const lastAnsweredDate = dates[0].toISOString().split('T')[0];

      // Calculate current streak
      let currentStreak = 0;
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      for (let i = 0; i < dates.length; i++) {
        const responseDate = new Date(dates[i]);
        responseDate.setHours(0, 0, 0, 0);

        const expectedDate = new Date(today);
        expectedDate.setDate(today.getDate() - i);

        if (responseDate.getTime() === expectedDate.getTime()) {
          currentStreak++;
        } else {
          break;
        }
      }

      // Calculate longest streak
      let longestStreak = 0;
      let tempStreak = 1;

      for (let i = 1; i < dates.length; i++) {
        const prevDate = new Date(dates[i - 1]);
        const currDate = new Date(dates[i]);

        const dayDiff = Math.floor((prevDate.getTime() - currDate.getTime()) / (1000 * 60 * 60 * 24));

        if (dayDiff === 1) {
          tempStreak++;
        } else {
          longestStreak = Math.max(longestStreak, tempStreak);
          tempStreak = 1;
        }
      }

      longestStreak = Math.max(longestStreak, tempStreak);

      return {
        current_streak: currentStreak,
        longest_streak: longestStreak,
        total_questions_answered: totalAnswered,
        last_answered_date: lastAnsweredDate
      };
    } catch (error) {
      logger.error('Error getting streak data:', error);
      return {
        current_streak: 0,
        longest_streak: 0,
        total_questions_answered: 0,
        last_answered_date: null
      };
    }
  }

  /**
   * Get questions by category
   */
  async getQuestionsByCategory(
    category: 'deep' | 'fun' | 'funny' | 'memories' | 'dreams' | 'gratitude',
    limit: number = 10
  ): Promise<DailyQuestion[]> {
    try {
      const { data, error } = await supabase
        .from('daily_questions')
        .select('*')
        .eq('category', category)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        logger.error('Failed to get questions by category:', error);
        return [];
      }

      return data.map(q => ({
        question_id: q.question_id,
        question_text: q.question_text,
        category: q.category,
        tone: q.tone,
        difficulty: q.difficulty,
        question_date: new Date().toISOString().split('T')[0] // Current date as placeholder
      }));
    } catch (error) {
      logger.error('Error getting questions by category:', error);
      return [];
    }
  }

  /**
   * Skip today's question (limited uses per month)
   */
  async skipTodaysQuestion(userId: string, coupleId: string): Promise<boolean> {
    try {
      // Check if user has skip attempts remaining (implement skip limit logic)
      // For now, we'll just log the skip event

      await logEvent('daily_question_skipped', {
        user_id: userId,
        couple_id: coupleId,
        skip_date: new Date().toISOString().split('T')[0]
      });

      return true;
    } catch (error) {
      logger.error('Error skipping question:', error);
      return false;
    }
  }
}

export const dailyQuestionsService = new DailyQuestionsService();
