/**
 * Streak Event Service
 *
 * Handles streak tracking events and calculations.
 * Works with both the current user_events structure and enhanced structure.
 * Provides a unified interface for tracking user engagement across all features.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import { simpleErrorService } from '../../shared/services/system/simpleErrorService';
import { logger } from '../../shared/utils/logger';
import { logEvent as centralLogEvent } from '../services/analytics/eventLogger';
import { supabase } from '../services/supabase/client';

export interface StreakEvent {
  user_id: string;
  couple_id?: string;
  event_name: string;
  event_category: string;
  event_type: string;
  streak_eligible: boolean;
  points_awarded: number;
  metadata: Record<string, any>;
}

export interface StreakCalculationResult {
  streak: number;
  lastActivityDate?: string;
  totalEvents: number;
}

export interface UserActivity {
  id: string;
  event_name: string;
  event_category?: string;
  event_type?: string;
  streak_eligible: boolean;
  points_awarded: number;
  created_at: string;
  metadata: Record<string, any>;
}

class StreakEventService {
  private static instance: StreakEventService;
  private enhancedColumnsAvailable: boolean | null = null;

  private constructor() {
    this.checkEnhancedColumns();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): StreakEventService {
    if (!StreakEventService.instance) {
      StreakEventService.instance = new StreakEventService();
    }
    return StreakEventService.instance;
  }
  /**
   * Track any user event with streak tracking
   */
  async trackEvent(event: StreakEvent): Promise<boolean> {
    try {
      // Validate input
      if (!this.validateStreakEvent(event)) {
        throw new Error('Invalid streak event data');
      }

      // Use the existing event logger for basic tracking
      await centralLogEvent(event.event_name, {
        ...event.metadata,
        event_category: event.event_category,
        event_type: event.event_type,
        streak_eligible: event.streak_eligible,
        points_awarded: event.points_awarded,
        couple_id: event.couple_id
      });

      // Check if enhanced columns are available
      const hasEnhancedColumns = await this.hasEnhancedColumns();

      if (hasEnhancedColumns) {
        // Insert with enhanced data
        const { error } = await supabase
          .from('user_events')
          .insert({
            user_id: event.user_id,
            couple_id: event.couple_id,
            event_name: event.event_name,
            event_category: event.event_category,
            event_type: event.event_type,
            streak_eligible: event.streak_eligible,
            points_awarded: event.points_awarded,
            metadata: event.metadata,
            created_at: new Date().toISOString()
          });

        if (error) throw error;
      } else {
        // Fall back to basic structure
        const { error } = await supabase
          .from('user_events')
          .insert({
            user_id: event.user_id,
            event_name: event.event_name,
            metadata: {
              ...event.metadata,
              event_category: event.event_category,
              event_type: event.event_type,
              streak_eligible: event.streak_eligible,
              points_awarded: event.points_awarded,
              couple_id: event.couple_id
            },
            created_at: new Date().toISOString()
          });

        if (error) throw error;
      }

      logger.info('Streak event tracked successfully:', event.event_type);
      return true;
    } catch (error) {
      await simpleErrorService.reportError(error as Error, {
        component: 'StreakEventService',
        action: 'trackEvent',
        metadata: {
          event_type: event.event_type,
          event_category: event.event_category,
          user_id: event.user_id
        },
        severity: 'medium'
      });

      logger.error('Error tracking streak event:', error);
      return false;
    }
  }

  /**
   * Calculate user's current streak with detailed result
   */
  async getUserStreak(userId: string, category?: string): Promise<StreakCalculationResult> {
    try {
      if (!userId || typeof userId !== 'string') {
        throw new Error('Invalid user ID provided');
      }

      // First try the database function if it exists
      const { data, error } = await supabase
        .rpc('calculate_user_streak', {
          p_user_id: userId,
          p_category: category
        });

      if (!error && data !== null) {
        const streak = data || 0;
        const activity = await this.getUserActivity(userId, 30); // Get last 30 days for context
        return {
          streak,
          lastActivityDate: activity[0]?.created_at,
          totalEvents: activity.length
        };
      }

      // Fallback to client-side calculation if function doesn't exist
      logger.warn('Database function not available, using client-side calculation');
      return await this.calculateStreakClientSide(userId, category);
    } catch (error) {
      await simpleErrorService.reportError(error as Error, {
        component: 'StreakEventService',
        action: 'getUserStreak',
        metadata: { userId, category },
        severity: 'low'
      });

      logger.error('Error calculating streak:', error);
      return { streak: 0, totalEvents: 0 };
    }
  }

  /**
   * Client-side streak calculation fallback
   */
  private async calculateStreakClientSide(userId: string, category?: string): Promise<StreakCalculationResult> {
    try {
      let query = supabase
        .from('user_events')
        .select('created_at, metadata, event_category')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // If we have enhanced columns, use them
      if (category) {
        query = query.eq('event_category', category);
      }

      const { data, error } = await query;

      if (error) throw error;
      if (!data || data.length === 0) {
        return { streak: 0, totalEvents: 0 };
      }

      // Filter for streak-eligible events
      const streakEvents = data.filter(event => {
        // Check if enhanced columns exist
        if (event.metadata && typeof event.metadata === 'object') {
          return event.metadata.streak_eligible === true;
        }
        return false;
      });

      if (streakEvents.length === 0) {
        return { streak: 0, totalEvents: 0 };
      }

      // Calculate consecutive days
      let streakCount = 0;
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      for (let i = 0; i < streakEvents.length; i++) {
        const eventDate = new Date(streakEvents[i].created_at);
        eventDate.setHours(0, 0, 0, 0);

        const expectedDate = new Date(today);
        expectedDate.setDate(today.getDate() - i);

        if (eventDate.getTime() === expectedDate.getTime()) {
          streakCount++;
        } else {
          break;
        }
      }

      return {
        streak: streakCount,
        lastActivityDate: streakEvents[0]?.created_at,
        totalEvents: streakEvents.length
      };
    } catch (error) {
      logger.error('Error in client-side streak calculation:', error);
      return { streak: 0, totalEvents: 0 };
    }
  }

  /**
   * Get user's recent activity
   */
  async getUserActivity(userId: string, days: number = 7): Promise<UserActivity[]> {
    try {
      if (!userId || typeof userId !== 'string') {
        throw new Error('Invalid user ID provided');
      }

      if (days < 1 || days > 365) {
        throw new Error('Days must be between 1 and 365');
      }

      const { data, error } = await supabase
        .from('user_events')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Filter for streak-eligible events
      return data.filter(event => {
        // Check if enhanced columns exist
        if (event.streak_eligible !== undefined) {
          return event.streak_eligible === true;
        }
        // Fallback to metadata check
        if (event.metadata && typeof event.metadata === 'object') {
          return event.metadata.streak_eligible === true;
        }
        return false;
      });
    } catch (error) {
      await simpleErrorService.reportError(error as Error, {
        component: 'StreakEventService',
        action: 'getUserActivity',
        metadata: { userId, days },
        severity: 'low'
      });

      logger.error('Error fetching user activity:', error);
      return [];
    }
  }

  /**
   * Check if enhanced columns are available
   */
  async hasEnhancedColumns(): Promise<boolean> {
    if (this.enhancedColumnsAvailable !== null) {
      return this.enhancedColumnsAvailable;
    }

    try {
      const { data, error } = await supabase
        .from('user_events')
        .select('event_category')
        .limit(1);

      this.enhancedColumnsAvailable = !error && data !== null;
      return this.enhancedColumnsAvailable;
    } catch (error) {
      this.enhancedColumnsAvailable = false;
      return false;
    }
  }

  /**
   * Check enhanced columns on initialization
   */
  private async checkEnhancedColumns(): Promise<void> {
    try {
      await this.hasEnhancedColumns();
    } catch (error) {
      logger.warn('Failed to check enhanced columns:', error);
    }
  }

  /**
   * Validate streak event data
   */
  private validateStreakEvent(event: StreakEvent): boolean {
    if (!event.user_id || typeof event.user_id !== 'string') {
      return false;
    }
    if (!event.event_name || typeof event.event_name !== 'string') {
      return false;
    }
    if (!event.event_category || typeof event.event_category !== 'string') {
      return false;
    }
    if (!event.event_type || typeof event.event_type !== 'string') {
      return false;
    }
    if (typeof event.streak_eligible !== 'boolean') {
      return false;
    }
    if (typeof event.points_awarded !== 'number' || event.points_awarded < 0) {
      return false;
    }
    if (!event.metadata || typeof event.metadata !== 'object') {
      return false;
    }
    return true;
  }
}

// Export singleton instance
export const streakEventService = StreakEventService.getInstance();

// Predefined event types for consistency
export const STREAK_EVENT_TYPES = {
  DAILY_QUESTION_ANSWERED: 'daily_question_answered',
  MATCH_GAME_COMPLETED: 'match_game_completed',
  MEAL_IDEA_FAVORITED: 'meal_idea_favorited',
  MEAL_IDEA_COMPLETED: 'meal_idea_completed',
  DATE_NIGHT_COMPLETED: 'date_night_completed',
  MILESTONE_ACHIEVED: 'milestone_achieved',
  TIMELINE_EVENT_CREATED: 'timeline_event_created',
} as const;

export const STREAK_EVENT_CATEGORIES = {
  DAILY_QUESTIONS: 'daily_questions',
  GAMES: 'games',
  MEALS: 'meals',
  DATE_NIGHTS: 'date_nights',
  MILESTONES: 'milestones',
  TIMELINE: 'timeline',
} as const;
