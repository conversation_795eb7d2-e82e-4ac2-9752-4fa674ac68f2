/**
 * Toast Utility
 * Cross-platform toast notifications
 */

export interface ToastOptions {
  duration?: 'short' | 'long';
  position?: 'top' | 'center' | 'bottom';
  type?: 'success' | 'error' | 'warning' | 'info';
  action?: {
    text: string;
    onPress: () => void;
  };
}

export interface ToastConfig {
  message: string;
  options?: ToastOptions;
}

class ToastManager {
  private toasts: ToastConfig[] = [];
  private listeners: ((toast: ToastConfig) => void)[] = [];

  show(message: string, options?: ToastOptions): void {
    const toast: ToastConfig = {
      message,
      options: {
        duration: 'short',
        position: 'bottom',
        type: 'info',
        ...options
      }
    };

    this.toasts.push(toast);
    this.notifyListeners(toast);

    // Auto-remove toast after duration
    const duration = options?.duration === 'long' ? 4000 : 2000;
    setTimeout(() => {
      this.remove(toast);
    }, duration);
  }

  success(message: string, options?: Omit<ToastOptions, 'type'>): void {
    this.show(message, { ...options, type: 'success' });
  }

  error(message: string, options?: Omit<ToastOptions, 'type'>): void {
    this.show(message, { ...options, type: 'error' });
  }

  warning(message: string, options?: Omit<ToastOptions, 'type'>): void {
    this.show(message, { ...options, type: 'warning' });
  }

  info(message: string, options?: Omit<ToastOptions, 'type'>): void {
    this.show(message, { ...options, type: 'info' });
  }

  private remove(toast: ToastConfig): void {
    const index = this.toasts.indexOf(toast);
    if (index > -1) {
      this.toasts.splice(index, 1);
    }
  }

  private notifyListeners(toast: ToastConfig): void {
    this.listeners.forEach(listener => listener(toast));
  }

  addListener(listener: (toast: ToastConfig) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  getActiveToasts(): ToastConfig[] {
    return [...this.toasts];
  }

  clear(): void {
    this.toasts = [];
  }
}

// Singleton instance
const toastManager = new ToastManager();

// Convenience functions
export const showToast = (message: string, options?: ToastOptions) => 
  toastManager.show(message, options);

export const showSuccess = (message: string, options?: Omit<ToastOptions, 'type'>) => 
  toastManager.success(message, options);

export const showError = (message: string, options?: Omit<ToastOptions, 'type'>) => 
  toastManager.error(message, options);

export const showWarning = (message: string, options?: Omit<ToastOptions, 'type'>) => 
  toastManager.warning(message, options);

export const showInfo = (message: string, options?: Omit<ToastOptions, 'type'>) => 
  toastManager.info(message, options);

export const clearToasts = () => toastManager.clear();

export const addToastListener = (listener: (toast: ToastConfig) => void) => 
  toastManager.addListener(listener);

// Default export for convenience
export default {
  show: showToast,
  success: showSuccess,
  error: showError,
  warning: showWarning,
  info: showInfo,
  clear: clearToasts,
  addListener: addToastListener
};
