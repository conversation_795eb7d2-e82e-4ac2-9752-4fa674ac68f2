/**
 * Configuration Manager
 * Re-export from the main configuration system
 */

// Re-export everything from the main configuration manager
export * from '../../src/shared/services/system/configurationManager';

// Import and re-export the main instances
import { 
  configManager as mainConfigManager,
  getValue as mainGetValue,
  isFeatureEnabled as mainIsFeatureEnabled
} from '../../src/shared/services/system/configurationManager';

export const configManager = mainConfigManager;
export const getValue = mainGetValue;
export const isFeatureEnabled = mainIsFeatureEnabled;
