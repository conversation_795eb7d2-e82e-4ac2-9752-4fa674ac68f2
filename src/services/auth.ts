/**
 * Authentication Service (src version)
 * Re-export from the main auth service
 */

// Re-export everything from the main auth service
export * from '../../services/auth';

// Import and re-export the main instances
import { 
  authService as mainAuthService,
  login as mainLogin,
  register as mainRegister,
  logout as mainLogout,
  getCurrentUser as mainGetCurrentUser,
  isAuthenticated as mainIsAuthenticated
} from '../../services/auth';

export const authService = mainAuthService;
export const login = mainLogin;
export const register = mainRegister;
export const logout = mainLogout;
export const getCurrentUser = mainGetCurrentUser;
export const isAuthenticated = mainIsAuthenticated;
