/**
 * Shared Components Index
 *
 * Re-exports from the new shared component system for backward compatibility
 */

// Re-export everything from the new shared components
export * from '../../shared/components/common';

// Legacy component mappings for backward compatibility
export { But<PERSON> as <PERSON>Button } from '../../shared/components/common/Button';
export { Card as DSCard } from '../../shared/components/common/Card';
export { Input as DSInput } from '../../shared/components/common/Input';

// Header and navigation components
export { default as DSHeaderBar, default as HamburgerMenu } from '../../shared/components/layout/HamburgerMenu';

// Couple-specific components
export {
    AchievementsSection, FavoritesSection, ProfileHeader
} from '../../shared/components/common/CoupleComponents';

// Daily question components
export { DailyQuestionCard } from '../../shared/components/common/DailyQuestionCard';
export { DailyQuestionsStreakCard } from '../../shared/components/common/DailyQuestionsStreakCard';

// Other components
export { StreakDisplay } from '../../shared/components/common/StreakDisplay';

// Create missing components as placeholders
export const StatsOverview = ({ stats, style, ...props }: any) => {
  return null; // Placeholder - implement actual stats overview component
};

// Avatar and badge components (create basic implementations)
export const DSAvatar = ({ source, size = 50, style, ...props }: any) => {
  return null; // Placeholder - implement actual avatar component
};

export const DSBadge = ({ children, variant = 'default', style, ...props }: any) => {
  return null; // Placeholder - implement actual badge component
};

// Theme provider
export { GlobalThemeProvider } from '../../shared/components/common/ThemeProvider';
