/**
 * Shared Components Index
 *
 * Re-exports from the new shared component system for backward compatibility
 */

// Re-export everything from the new shared components
export * from '../../shared/components/common';

// Legacy component mappings for backward compatibility
export { But<PERSON> as <PERSON>Button } from '../../shared/components/common/Button';
export { Card as DSCard } from '../../shared/components/common/Card';
export { Input as DSInput } from '../../shared/components/common/Input';

// Header and navigation components
export { default as DSHeaderBar, default as HamburgerMenu } from '../../shared/components/layout/HamburgerMenu';

// Couple-specific components
export {
    AchievementsSection, FavoritesSection, ProfileHeader
} from '../../shared/components/common/CoupleComponents';

// Daily question components
export { DailyQuestionCard } from '../../shared/components/common/DailyQuestionCard';
export { DailyQuestionsStreakCard } from '../../shared/components/common/DailyQuestionsStreakCard';

// Other components
export { StreakDisplay } from '../../shared/components/common/StreakDisplay';

// Create missing components as placeholders
export const StatsOverview = ({ stats, style, ...props }: any) => {
  return null; // Placeholder - implement actual stats overview component
};

// Avatar and badge components (create basic implementations)
export const DSAvatar = ({ source, size = 50, style, ...props }: any) => {
  return null; // Placeholder - implement actual avatar component
};

export const DSBadge = ({ children, variant = 'default', style, ...props }: any) => {
  return null; // Placeholder - implement actual badge component
};

// Theme provider
export { GlobalThemeProvider } from '../../shared/components/common/ThemeProvider';

// Missing components - create placeholders
export const DSGlassCard = ({ children, style, ...props }: any) => {
  return null; // Placeholder - implement actual glass card component
};

export const ScreenLayout = ({ children, style, ...props }: any) => {
  return null; // Placeholder - implement actual screen layout component
};

// Check if constants file exists and export FEATURE_FLAGS
try {
  const { FEATURE_FLAGS } = require('../../utils/constants');
  export { FEATURE_FLAGS };
} catch (e) {
  // Create placeholder if constants file doesn't exist
  export const FEATURE_FLAGS = {
    ENABLE_DAILY_QUESTIONS: true,
    ENABLE_MATCH_GAME: true,
    ENABLE_DATE_NIGHT_IDEAS: true,
  };
}
