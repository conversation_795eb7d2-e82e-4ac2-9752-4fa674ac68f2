/**
 * Common Components Index
 * Exports commonly used components across the app
 */

// Theme Components
export { ThemeProvider, useTheme, useThemeColors, useThemeSpacing, useThemeTypography } from './ThemeProvider';

// Button Components
export const Button = ({ children, onPress, style, variant = 'primary', disabled = false, ...props }: any) => {
  return null; // Placeholder - implement actual button component
};

export const IconButton = ({ icon, onPress, style, size = 'medium', ...props }: any) => {
  return null; // Placeholder - implement actual icon button component
};

// Input Components
export const Input = ({ value, onChangeText, placeholder, style, ...props }: any) => {
  return null; // Placeholder - implement actual input component
};

export const TextInput = ({ value, onChangeText, placeholder, multiline = false, style, ...props }: any) => {
  return null; // Placeholder - implement actual text input component
};

// Layout Components
export const Card = ({ children, style, variant = 'default', ...props }: any) => {
  return null; // Placeholder - implement actual card component
};

export const Container = ({ children, style, padding = true, ...props }: any) => {
  return null; // Placeholder - implement actual container component
};

export const Spacer = ({ size = 'medium', horizontal = false, ...props }: any) => {
  return null; // Placeholder - implement actual spacer component
};

// Typography Components
export const Text = ({ children, style, variant = 'body', ...props }: any) => {
  return null; // Placeholder - implement actual text component
};

export const Heading = ({ children, level = 1, style, ...props }: any) => {
  return null; // Placeholder - implement actual heading component
};

// Loading Components
export const LoadingSpinner = ({ size = 'medium', color, style, ...props }: any) => {
  return null; // Placeholder - implement actual loading spinner component
};

export const LoadingOverlay = ({ visible = false, message, ...props }: any) => {
  return null; // Placeholder - implement actual loading overlay component
};

// Feedback Components
export const EmptyState = ({ title, message, icon, action, style, ...props }: any) => {
  return null; // Placeholder - implement actual empty state component
};

export const ErrorMessage = ({ error, onRetry, style, ...props }: any) => {
  return null; // Placeholder - implement actual error message component
};

// Modal Components
export const Modal = ({ visible, onClose, children, style, ...props }: any) => {
  return null; // Placeholder - implement actual modal component
};

export const BottomSheet = ({ visible, onClose, children, height, ...props }: any) => {
  return null; // Placeholder - implement actual bottom sheet component
};

// List Components
export const List = ({ data, renderItem, keyExtractor, style, ...props }: any) => {
  return null; // Placeholder - implement actual list component
};

export const ListItem = ({ title, subtitle, onPress, leftIcon, rightIcon, style, ...props }: any) => {
  return null; // Placeholder - implement actual list item component
};

// Avatar Components
export const Avatar = ({ source, size = 50, name, style, ...props }: any) => {
  return null; // Placeholder - implement actual avatar component
};

export const AvatarGroup = ({ avatars, max = 3, size = 40, style, ...props }: any) => {
  return null; // Placeholder - implement actual avatar group component
};

// Badge Components
export const Badge = ({ children, variant = 'default', color, style, ...props }: any) => {
  return null; // Placeholder - implement actual badge component
};

export const StatusBadge = ({ status, style, ...props }: any) => {
  return null; // Placeholder - implement actual status badge component
};

// Form Components
export const Form = ({ children, onSubmit, style, ...props }: any) => {
  return null; // Placeholder - implement actual form component
};

export const FormField = ({ label, error, children, required = false, style, ...props }: any) => {
  return null; // Placeholder - implement actual form field component
};

export const Checkbox = ({ checked, onPress, label, style, ...props }: any) => {
  return null; // Placeholder - implement actual checkbox component
};

export const Switch = ({ value, onValueChange, label, style, ...props }: any) => {
  return null; // Placeholder - implement actual switch component
};

// Progress Components
export const ProgressBar = ({ progress, style, color, ...props }: any) => {
  return null; // Placeholder - implement actual progress bar component
};

export const CircularProgress = ({ progress, size = 50, color, style, ...props }: any) => {
  return null; // Placeholder - implement actual circular progress component
};

// Image Components
export const Image = ({ source, style, resizeMode = 'cover', ...props }: any) => {
  return null; // Placeholder - implement actual image component
};

export const ImagePicker = ({ onImageSelected, style, ...props }: any) => {
  return null; // Placeholder - implement actual image picker component
};

// Navigation Components
export const TabBar = ({ tabs, activeTab, onTabPress, style, ...props }: any) => {
  return null; // Placeholder - implement actual tab bar component
};

export const NavigationHeader = ({ title, leftAction, rightAction, style, ...props }: any) => {
  return null; // Placeholder - implement actual navigation header component
};

// Divider Components
export const Divider = ({ style, color, thickness = 1, ...props }: any) => {
  return null; // Placeholder - implement actual divider component
};

// Toast Components
export const Toast = ({ message, type = 'info', duration = 3000, ...props }: any) => {
  return null; // Placeholder - implement actual toast component
};

// Couple-specific Components
export const CoupleComponents = {
  AchievementsSection: ({ achievements, style, ...props }: any) => null,
  FavoritesSection: ({ favorites, onFavoritePress, style, ...props }: any) => null,
  ProfileHeader: ({ user, partner, style, ...props }: any) => null,
  DailyQuestionCard: ({ question, onAnswer, style, ...props }: any) => null,
  StreakDisplay: ({ streak, style, ...props }: any) => null,
  StatsOverview: ({ stats, style, ...props }: any) => null
};

// Export individual couple components
export const { 
  AchievementsSection, 
  FavoritesSection, 
  ProfileHeader,
  DailyQuestionCard,
  StreakDisplay,
  StatsOverview
} = CoupleComponents;
