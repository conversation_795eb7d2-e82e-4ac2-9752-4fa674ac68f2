/**
 * Theme Provider Component
 * Provides theme context to the application
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { tokens } from '../../utils/theme/tokens';

export interface Theme {
  colors: typeof tokens.themes.light;
  spacing: typeof tokens.spacing;
  typography: typeof tokens.typography;
  borderRadius: typeof tokens.borderRadius;
  shadows: typeof tokens.shadows;
  glassMorphism: typeof tokens.glassMorphism;
  animation: typeof tokens.animation;
  isDark: boolean;
}

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (themeName: 'light' | 'dark') => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
  initialTheme?: 'light' | 'dark';
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  initialTheme = 'light' 
}) => {
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>(initialTheme);

  // Load theme from storage on mount
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const stored = localStorage.getItem('app_theme');
        if (stored && (stored === 'light' || stored === 'dark')) {
          setCurrentTheme(stored);
        }
      } catch (error) {
        console.error('Failed to load theme from storage:', error);
      }
    };

    loadTheme();
  }, []);

  // Save theme to storage when it changes
  useEffect(() => {
    try {
      localStorage.setItem('app_theme', currentTheme);
    } catch (error) {
      console.error('Failed to save theme to storage:', error);
    }
  }, [currentTheme]);

  const theme: Theme = {
    colors: tokens.themes[currentTheme],
    spacing: tokens.spacing,
    typography: tokens.typography,
    borderRadius: tokens.borderRadius,
    shadows: tokens.shadows,
    glassMorphism: tokens.glassMorphism,
    animation: tokens.animation,
    isDark: currentTheme === 'dark'
  };

  const toggleTheme = () => {
    setCurrentTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  const setTheme = (themeName: 'light' | 'dark') => {
    setCurrentTheme(themeName);
  };

  const contextValue: ThemeContextType = {
    theme,
    toggleTheme,
    setTheme
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Legacy export for backward compatibility
export const GlobalThemeProvider = ThemeProvider;

// Hook for accessing theme colors directly
export const useThemeColors = () => {
  const { theme } = useTheme();
  return theme.colors;
};

// Hook for accessing theme spacing directly
export const useThemeSpacing = () => {
  const { theme } = useTheme();
  return theme.spacing;
};

// Hook for accessing theme typography directly
export const useThemeTypography = () => {
  const { theme } = useTheme();
  return theme.typography;
};

// Utility function to create themed styles
export const createThemedStyles = <T extends Record<string, any>>(
  styleFactory: (theme: Theme) => T
) => {
  return (theme: Theme): T => styleFactory(theme);
};

// Higher-order component for theme injection
export const withTheme = <P extends object>(
  Component: React.ComponentType<P & { theme: Theme }>
) => {
  return React.forwardRef<any, P>((props, ref) => {
    const { theme } = useTheme();
    return <Component {...props} theme={theme} ref={ref} />;
  });
};

export default ThemeProvider;
