/**
 * Hamburger Menu Component
 * Navigation menu for the application
 */

import React, { useState } from 'react';

export interface MenuItem {
  id: string;
  title: string;
  icon?: string;
  onPress: () => void;
  badge?: number;
  disabled?: boolean;
}

export interface HamburgerMenuProps {
  visible: boolean;
  onClose: () => void;
  menuItems: MenuItem[];
  user?: {
    name: string;
    avatar?: string;
    email?: string;
  };
  partner?: {
    name: string;
    avatar?: string;
    email?: string;
  };
  style?: any;
}

const HamburgerMenu: React.FC<HamburgerMenuProps> = ({
  visible,
  onClose,
  menuItems,
  user,
  partner,
  style,
  ...props
}) => {
  if (!visible) return null;

  return (
    <div style={{ 
      position: 'fixed', 
      top: 0, 
      left: 0, 
      right: 0, 
      bottom: 0, 
      backgroundColor: 'rgba(0,0,0,0.5)', 
      zIndex: 1000,
      ...style 
    }}>
      <div style={{
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        width: '280px',
        backgroundColor: 'white',
        padding: '20px',
        boxShadow: '2px 0 10px rgba(0,0,0,0.1)'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '20px', borderBottom: '1px solid #eee', paddingBottom: '20px' }}>
          <button 
            onClick={onClose}
            style={{ 
              float: 'right', 
              background: 'none', 
              border: 'none', 
              fontSize: '24px',
              cursor: 'pointer'
            }}
          >
            ×
          </button>
          
          {/* User Profile Section */}
          {user && (
            <div style={{ marginTop: '10px' }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '20px',
                  backgroundColor: '#9CAF88',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 'bold',
                  marginRight: '10px'
                }}>
                  {user.name.charAt(0).toUpperCase()}
                </div>
                <div>
                  <div style={{ fontWeight: 'bold', fontSize: '16px' }}>{user.name}</div>
                  {user.email && (
                    <div style={{ fontSize: '12px', color: '#666' }}>{user.email}</div>
                  )}
                </div>
              </div>
              
              {/* Partner Info */}
              {partner && (
                <div style={{ display: 'flex', alignItems: 'center', fontSize: '14px', color: '#666' }}>
                  <span>Connected with </span>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '10px',
                    backgroundColor: '#CBC3E3',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '10px',
                    fontWeight: 'bold',
                    margin: '0 5px'
                  }}>
                    {partner.name.charAt(0).toUpperCase()}
                  </div>
                  <span style={{ fontWeight: '500' }}>{partner.name}</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Menu Items */}
        <div>
          {menuItems.map((item) => (
            <button
              key={item.id}
              onClick={() => {
                if (!item.disabled) {
                  item.onPress();
                  onClose();
                }
              }}
              disabled={item.disabled}
              style={{
                width: '100%',
                padding: '15px 10px',
                border: 'none',
                backgroundColor: 'transparent',
                textAlign: 'left',
                cursor: item.disabled ? 'not-allowed' : 'pointer',
                opacity: item.disabled ? 0.5 : 1,
                borderRadius: '8px',
                marginBottom: '5px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                fontSize: '16px',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => {
                if (!item.disabled) {
                  e.currentTarget.style.backgroundColor = '#f5f5f5';
                }
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {item.icon && (
                  <span style={{ marginRight: '10px', fontSize: '18px' }}>
                    {item.icon}
                  </span>
                )}
                <span>{item.title}</span>
              </div>
              
              {item.badge && item.badge > 0 && (
                <div style={{
                  backgroundColor: '#EF4444',
                  color: 'white',
                  borderRadius: '10px',
                  padding: '2px 6px',
                  fontSize: '12px',
                  fontWeight: 'bold',
                  minWidth: '18px',
                  textAlign: 'center'
                }}>
                  {item.badge > 99 ? '99+' : item.badge}
                </div>
              )}
            </button>
          ))}
        </div>

        {/* Footer */}
        <div style={{ 
          position: 'absolute', 
          bottom: '20px', 
          left: '20px', 
          right: '20px',
          borderTop: '1px solid #eee',
          paddingTop: '20px',
          fontSize: '12px',
          color: '#999',
          textAlign: 'center'
        }}>
          Everlasting Us v1.0.0
        </div>
      </div>
    </div>
  );
};

export default HamburgerMenu;
