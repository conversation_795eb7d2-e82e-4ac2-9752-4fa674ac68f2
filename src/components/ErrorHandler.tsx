/**
 * <PERSON><PERSON><PERSON> Handler Component
 * Handles and displays errors throughout the application
 */

import React, { Component, ReactNode, ErrorInfo } from 'react';

export interface ErrorHandlerProps {
  children: ReactNode;
  fallback?: (error: Error, errorInfo: ErrorInfo) => ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

export interface ErrorHandlerState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorHandler extends Component<ErrorHandlerProps, ErrorHandlerState> {
  constructor(props: ErrorHandlerProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorHandlerState> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> caught an error:', error);
      console.error('Error info:', errorInfo);
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // In production, you might want to log to an error reporting service
    // Example: Sentry.captureException(error, { contexts: { react: errorInfo } });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback && this.state.error && this.state.errorInfo) {
        return this.props.fallback(this.state.error, this.state.errorInfo);
      }

      // Default error UI
      return (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          backgroundColor: '#FEE2E2',
          border: '1px solid #FECACA',
          borderRadius: '8px',
          margin: '20px',
          color: '#991B1B'
        }}>
          <h2 style={{ 
            fontSize: '24px', 
            fontWeight: 'bold', 
            marginBottom: '10px',
            color: '#991B1B'
          }}>
            Oops! Something went wrong
          </h2>
          
          <p style={{ 
            fontSize: '16px', 
            marginBottom: '20px',
            color: '#7F1D1D'
          }}>
            We're sorry, but something unexpected happened. Please try again.
          </p>

          {process.env.NODE_ENV === 'development' && this.state.error && (
            <details style={{
              backgroundColor: '#FEF2F2',
              border: '1px solid #FECACA',
              borderRadius: '4px',
              padding: '10px',
              marginBottom: '20px',
              textAlign: 'left'
            }}>
              <summary style={{ 
                cursor: 'pointer', 
                fontWeight: 'bold',
                marginBottom: '10px'
              }}>
                Error Details (Development Only)
              </summary>
              <pre style={{
                fontSize: '12px',
                overflow: 'auto',
                backgroundColor: '#FFFFFF',
                padding: '10px',
                borderRadius: '4px',
                border: '1px solid #E5E7EB'
              }}>
                {this.state.error.toString()}
                {this.state.errorInfo?.componentStack}
              </pre>
            </details>
          )}

          <button
            onClick={this.handleRetry}
            style={{
              backgroundColor: '#9CAF88',
              color: 'white',
              border: 'none',
              padding: '12px 24px',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#7A8B6A';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#9CAF88';
            }}
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook-based error handler for functional components
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = () => setError(null);

  const captureError = (error: Error) => {
    setError(error);
    
    // Log error in development
    if (process.env.NODE_ENV === 'development') {
      console.error('useErrorHandler caught an error:', error);
    }
  };

  // Throw error to be caught by nearest ErrorHandler
  if (error) {
    throw error;
  }

  return { captureError, resetError };
};

// Higher-order component for error handling
export const withErrorHandler = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: (error: Error, errorInfo: ErrorInfo) => ReactNode
) => {
  return React.forwardRef<any, P>((props, ref) => (
    <ErrorHandler fallback={fallback}>
      <Component {...props} ref={ref} />
    </ErrorHandler>
  ));
};

// Async error boundary for handling promise rejections
export const AsyncErrorHandler: React.FC<{ children: ReactNode }> = ({ children }) => {
  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      // You could show a toast notification or log to an error service here
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <>{children}</>;
};

export default ErrorHandler;
