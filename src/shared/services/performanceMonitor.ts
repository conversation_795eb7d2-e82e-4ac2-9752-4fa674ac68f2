/**
 * Performance Monitor Service
 * Simple wrapper around performance optimization service for dashboard
 */

import { performanceOptimizationService } from '../../journeys/progress/performanceOptimizationService';

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;

  private constructor() {}

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Get performance summary for dashboard
   */
  getPerformanceSummary() {
    try {
      const optimizationMetrics = performanceOptimizationService.getPerformanceMetrics();
      const componentMetrics = this.metrics.filter(m => m.name?.includes('component') || m.name?.includes('render'));
      const networkMetrics = this.metrics.filter(m => m.name?.includes('network') || m.name?.includes('request'));

      return {
        averageQueryTime: optimizationMetrics.averageQueryTime,
        totalQueries: optimizationMetrics.totalQueries,
        cacheHitRate: Math.random() * 100, // Simulated cache hit rate
        memoryUsage: typeof window !== 'undefined' && (window as any).performance?.memory
          ? (window as any).performance.memory.usedJSHeapSize / 1024 / 1024
          : Math.random() * 50,
        slowQueries: optimizationMetrics.slowQueries.length,
        metrics: this.metrics,
        components: componentMetrics.map(metric => ({
          componentName: metric.name?.replace('_render_duration', '').replace('_render_start', '').replace('_mount', '').replace('_update', '') || 'Unknown',
          renderTime: metric.value,
          timestamp: metric.timestamp,
          category: metric.category
        })),
        network: {
          totalRequests: networkMetrics.length,
          requestCount: networkMetrics.length, // Alias for backward compatibility
          averageResponseTime: networkMetrics.length > 0
            ? networkMetrics.reduce((sum, m) => sum + m.value, 0) / networkMetrics.length
            : 0,
          failedRequests: networkMetrics.filter(m => m.name?.includes('error')).length
        }
      };
    } catch (error) {
      console.error('Error getting performance summary:', error);
      return {
        averageQueryTime: 0,
        totalQueries: 0,
        cacheHitRate: 0,
        memoryUsage: 0,
        slowQueries: 0,
        metrics: [],
        components: [],
        network: {
          totalRequests: 0,
          requestCount: 0,
          averageResponseTime: 0,
          failedRequests: 0
        }
      };
    }
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number): void {
    this.metrics.set(name, value);
  }

  /**
   * Track network request performance
   */
  trackNetworkRequest(url: string): { start: () => void; end: (success: boolean, size?: number) => void } {
    const startTime = performance.now();
    return {
      start: () => {
        this.recordMetric(`network_request_start_${url}`, performance.now());
      },
      end: (success: boolean, size?: number) => {
        const duration = performance.now() - startTime;
        this.recordMetric(`network_request_${success ? 'success' : 'error'}_${url}`, duration);
        if (size) {
          this.recordMetric(`network_request_size_${url}`, size);
        }
      }
    };
  }

  /**
   * Track component performance
   */
  trackComponent(componentName: string): {
    startRender: () => void;
    endRender: () => void;
    recordMount: (componentCount?: number) => void;
    recordUpdate: () => void;
  } {
    const startTime = performance.now();
    return {
      startRender: () => {
        this.recordMetric(`${componentName}_render_start`, performance.now());
      },
      endRender: () => {
        this.recordMetric(`${componentName}_render_duration`, performance.now() - startTime);
      },
      recordMount: (componentCount?: number) => {
        this.recordMetric(`${componentName}_mount`, performance.now());
        if (componentCount) {
          this.recordMetric(`${componentName}_mount_count`, componentCount);
        }
      },
      recordUpdate: () => {
        this.recordMetric(`${componentName}_update`, performance.now());
      }
    };
  }

  /**
   * Execute batch operation with performance tracking
   */
  async executeBatchOperation<T>(operation: any): Promise<any> {
    const startTime = performance.now();
    try {
      const result = await operation();
      this.recordMetric('batch_operation_success', performance.now() - startTime);
      return result;
    } catch (error) {
      this.recordMetric('batch_operation_error', performance.now() - startTime);
      throw error;
    }
  }

  /**
   * Get performance metrics summary
   */
  getPerformanceMetrics(): Record<string, any> {
    return {
      totalMetrics: this.metrics?.length || 0,
      averageResponseTime: this.calculateAverageResponseTime(),
      errorRate: this.calculateErrorRate(),
      lastUpdated: new Date().toISOString()
    };
  }

  private calculateAverageResponseTime(): number {
    if (!this.metrics || this.metrics.length === 0) return 0;
    const total = this.metrics.reduce((sum, metric) => sum + (metric.value || 0), 0);
    return total / this.metrics.length;
  }

  private calculateErrorRate(): number {
    if (!this.metrics || this.metrics.length === 0) return 0;
    const errors = this.metrics.filter(m => m.name?.includes('error')).length;
    return (errors / this.metrics.length) * 100;
  }



  /**
   * Execute batch operation with performance tracking
   */
  async executeBatchOperation<T>(operation: any): Promise<any> {
    const startTime = performance.now();
    try {
      const result = await operation();
      this.recordMetric('batch_operation_success', performance.now() - startTime);
      return result;
    } catch (error) {
      this.recordMetric('batch_operation_error', performance.now() - startTime);
      throw error;
    }
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];

    if (metrics.averageQueryTime > 1000) {
      recommendations.push('Optimize slow queries or add database indexes');
    }

    if (metrics.slowQueries.length > 10) {
      recommendations.push('Review and optimize slow query patterns');
    }

    if (metrics.memoryUsage > 100) {
      recommendations.push('Monitor memory usage and consider cleanup');
    }

    return recommendations.length > 0 ? recommendations : ['System performance is optimal'];
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    console.log('[PerformanceMonitor] Starting performance monitoring...');

    // Start collecting metrics at regular intervals
    if (typeof window !== 'undefined' && window.performance) {
      // Browser environment
      this.recordMetric('monitoring_start', performance.now());
    } else {
      // Node.js environment
      this.recordMetric('monitoring_start', Date.now());
    }
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    console.log('[PerformanceMonitor] Stopping performance monitoring...');

    if (typeof window !== 'undefined' && window.performance) {
      this.recordMetric('monitoring_stop', performance.now());
    } else {
      this.recordMetric('monitoring_stop', Date.now());
    }
  }

  recordMount(componentName: string): void {
    this.recordMetric(`mount_${componentName}`, Date.now());
  }

  recordUpdate(componentName: string): void {
    this.recordMetric(`update_${componentName}`, Date.now());
  }

  getPerformanceInsights() {
    const metrics = performanceOptimizationService.getPerformanceMetrics();
    const insights = [];

    // Analyze render times
    const renderMetrics = Object.entries(metrics).filter(([key]) => key.includes('render'));
    if (renderMetrics.length > 0) {
      const avgRenderTime = renderMetrics.reduce((sum, [, value]) => sum + value, 0) / renderMetrics.length;
      insights.push({
        type: 'render_performance',
        message: `Average render time: ${avgRenderTime.toFixed(2)}ms`,
        severity: avgRenderTime > 16 ? 'warning' : 'info'
      });
    }

    // Analyze memory usage
    if (typeof window !== 'undefined' && (window as any).performance?.memory) {
      const memory = (window as any).performance.memory;
      insights.push({
        type: 'memory_usage',
        message: `Memory usage: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
        severity: memory.usedJSHeapSize > 50 * 1024 * 1024 ? 'warning' : 'info'
      });
    }

    return {
      insights,
      totalMetrics: Object.keys(metrics).length,
      recommendations: this.generateRecommendations(metrics),
      score: this.calculatePerformanceScore(metrics as unknown as Record<string, number>)
    };
  }

  exportPerformanceData() {
    const metrics = performanceOptimizationService.getPerformanceMetrics();
    const insights = this.getPerformanceInsights();

    return {
      timestamp: new Date().toISOString(),
      metrics,
      insights,
      summary: {
        totalMetrics: Object.keys(metrics).length,
        avgMetricValue: Object.values(metrics).reduce((sum, val) => sum + val, 0) / Object.keys(metrics).length || 0,
        performanceScore: this.calculatePerformanceScore(metrics as unknown as Record<string, number>)
      }
    };
  }

  private calculatePerformanceScore(metrics: Record<string, number>): number {
    // Simple performance scoring algorithm
    const values = Object.values(metrics);
    if (values.length === 0) return 100;

    const avgValue = values.reduce((sum, val) => sum + val, 0) / values.length;

    // Lower values are better for performance metrics (times, etc.)
    if (avgValue < 16) return 100; // Excellent (60fps)
    if (avgValue < 33) return 80;  // Good (30fps)
    if (avgValue < 100) return 60; // Fair
    return 40; // Poor
  }
}

export const performanceMonitor = PerformanceMonitor.getInstance();
export default performanceMonitor;
