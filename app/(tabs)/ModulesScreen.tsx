import { router } from 'expo-router';
import { Calendar, CircleCheck as CheckCircle, Clock, Heart, Lock, MessageCircle, Star, Users } from 'lucide-react-native';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { DSButton, DSHeaderBar } from '../../src/components/shared';
import { useAuth } from '../../src/journeys/onboarding/useAuth';
import { usePointsSystemSupabase } from '../../src/journeys/progress/usePointsSystemSupabase';
import HamburgerMenu from '../../src/shared/components/layout/HamburgerMenu';
import { colors } from '../../src/shared/utils/colors';

export default function ModulesScreen() {
  const { isInitialized } = useAuth();
  const { totalPoints, addPoints } = usePointsSystemSupabase();

  // Don't render until auth is initialized
  if (!isInitialized) {
    return null;
  }


  // Fallback colors in case colors object is undefined (updated to new brand colors - no gradients)
  const fallbackColors = {
    primary: '#9CAF88',        // Green Sage
    secondary: '#CBC3E3',      // Lavender Purple
    solidColors: {
      primary: '#9CAF88',
      warm: '#F3E0DA',
      secondary: '#CBC3E3',
      header: '#9CAF88'
    }
  };

  const safeColors = colors || fallbackColors;

  // Complete modules array with all 12 weeks
  const modules = [
    {
      week: 1,
      title: 'Getting to Know You',
      description: 'Rediscover each other through fun activities',
      completed: false,
      locked: false,
      activities: ['The Match Game', 'Date Night Plan', 'Chat Prompts', 'Soft Start-Up'],
      color: safeColors.solidColors.primary,
      points: 0,
    },
    {
      week: 0,
      title: 'Date Night Ideas',
      description: 'Plan perfect evenings with activities + meals',
      completed: false,
      locked: false,
      activities: ['Date Night Planning', 'Meal Decision-Making', 'Combined Suggestions', 'Evening Planning'],
      color: safeColors.solidColors.warm,
      points: 0,
      isSpecial: true,
    },
    {
      week: 2,
      title: 'Strengths Bingo',
      description: 'Celebrate each other\'s strengths and build positivity',
      completed: false,
      locked: false,
      activities: ['Strengths Bingo', 'Date Night Plan', 'Chat Prompts', '5:1 Ratio'],
      color: safeColors.solidColors.secondary,
      points: 0,
    },
    {
      week: 3,
      title: 'Would You Rather?',
      description: 'Explore quirks, pet peeves, and fears playfully',
      completed: false,
      locked: false,
      activities: ['Would You Rather?', 'Alphabet Date Night', 'Chat Prompts', 'Being Curious'],
      color: safeColors.solidColors.primary,
      points: 0,
    },
    {
      week: 4,
      title: 'Create a Playlist',
      description: 'Express your story through music and creative connection',
      completed: false,
      locked: false,
      activities: ['Create a Playlist', 'Movie Theme Night', 'Chat Prompts', 'Emotional Regulation'],
      color: safeColors.solidColors.warm,
      points: 0,
    },
    {
      week: 5,
      title: 'Dream Vacation',
      description: 'Imagine and plan your ideal vacation together',
      completed: false,
      locked: false,
      activities: ['Dream Vacation', 'Date Night Plan', 'Chat Prompts', 'Conflict Style'],
      color: safeColors.solidColors.secondary,
      points: 0,
    },
    {
      week: 6,
      title: 'Sharing Memories',
      description: 'Share childhood moments and create a memory lane together',
      completed: false,
      locked: false,
      activities: ['Sharing Memories', 'Memory Lane Date', 'Chat Prompts', 'Validation Toolkit'],
      color: safeColors.solidColors.primary,
      points: 0,
    },
    {
      week: 7,
      title: 'Superhero Duo',
      description: 'Create your superhero identities and discover your combined powers',
      completed: false,
      locked: false,
      activities: ['Superhero Duo Chart', 'Thrift Shop Showdown', 'Chat Prompts', 'Turning Toward'],
      color: safeColors.solidColors.warm,
      points: 0,
    },
    {
      week: 8,
      title: 'The Perfect Saturday',
      description: 'Design your ideal day together and explore deeper needs',
      completed: false,
      locked: false,
      activities: ['Perfect Saturday Game', 'Blended Perfect Day', 'Chat Prompts', 'Conflict Mapping'],
      color: safeColors.solidColors.secondary,
      points: 0,
    },
    {
      week: 9,
      title: 'Custom Crest',
      description: 'Design your family crest and explore shared values',
      completed: false,
      locked: false,
      activities: ['Create a Crest', 'Live Show Date', 'Chat Prompts', 'Shared Values'],
      color: safeColors.solidColors.primary,
      points: 0,
    },
    {
      week: 10,
      title: 'The Dream Bank',
      description: 'Plan your financial dreams and have meaningful money conversations',
      completed: false,
      locked: false,
      activities: ['Dream Bank ($5M)', 'Get Artsy Date', 'Chat Prompts', 'Money Talk'],
      color: safeColors.solidColors.warm,
      points: 0,
    },
    {
      week: 11,
      title: 'Love Languages',
      description: 'Discover your love languages and practice meeting each other\'s needs',
      completed: false,
      locked: false,
      activities: ['Love Language Quiz', 'Mini-Olympics', 'Chat Prompts', 'Love Language Practice'],
      color: safeColors.solidColors.secondary,
      points: 0,
    },
    {
      week: 12,
      title: 'Playful Tales',
      description: 'Create stories together and explore intimate connection',
      completed: false,
      locked: false,
      activities: ['Build-a-Story Game', 'Get Active Date', 'Chat Prompts', 'Sensate Focus'],
      color: safeColors.solidColors.primary,
      points: 0,
    },
  ];

  // Ensure modules array is always defined
  const safeModules = modules || [];


  // Check if activity is completed
  const isActivityCompleted = (moduleId: number, activityName: string) => {
    // TODO: Implement activity completion tracking with Supabase
    return false;
  };

  const renderActivityIcon = (activity: string) => {
    switch (activity) {
      case 'The Match Game':
      case 'Active Listening':
      case 'Create a Playlist':
        return <Users size={16} color={colors.white} />;
      case 'Date Night Plan':
      case 'Alphabet Date Night':
        return <Calendar size={16} color={colors.white} />;
      case 'Chat Prompts':
      case 'Soft Start-Up':
      case '5:1 Ratio':
      case 'Chat Prompts':
      case 'Emotional Regulation':
        return <MessageCircle size={16} color={colors.white} />;
      case 'Movie Theme Night':
        return <Calendar size={16} color={colors.white} />;
      default:
        return <Heart size={16} color={colors.white} />;
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Hamburger Menu */}
      <HamburgerMenu position="top-right" />

      {/* Header */}
      <DSHeaderBar
        title="Weekly Modules"
        tone="sage"
      />
      <View style={styles.headerSubtitleContainer}>
        <Text style={styles.headerSubtitle}>Complete activities together to strengthen your relationship</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {Array.isArray(safeModules) && safeModules.length > 0 ? (
          safeModules.map((module, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.moduleCard, module.locked && styles.moduleCardLocked]}
              disabled={module.locked}
              onPress={() => {
                if (module.week === 0 && module.isSpecial) {
                  router.push('/(tabs)/date-night');
                } else if (module.week === 1) {
                  router.push('/week-one');
                } else if (module.week === 2) {
                  router.push('/week-two');
                } else if (module.week === 3) {
                  router.push('/week-three');
                } else if (module.week === 4) {
                  router.push('/week-four');
                } else if (module.week === 5) {
                  router.push('/week-five');
                } else if (module.week === 6) {
                  router.push('/week-six');
                } else if (module.week === 7) {
                  router.push('/week-seven');
                } else if (module.week === 8) {
                  router.push('/week-eight');
                } else if (module.week === 9) {
                  router.push('/week-nine');
                } else if (module.week === 10) {
                  router.push('/week-ten');
                } else if (module.week === 11) {
                  router.push('/week-eleven');
                } else if (module.week === 12) {
                  router.push('/week-twelve');
                }
              }}
            >
              <View style={styles.moduleHeader}>
                <View style={styles.moduleWeek}>
                  <Text style={styles.moduleWeekText}>
                    {module.isSpecial ? '✨ Special' : `Week ${module.week}`}
                  </Text>
                </View>

                <View style={styles.moduleStatus}>
                  {module.completed ? (
                    <CheckCircle size={24} color={colors.success} />
                  ) : module.locked ? (
                    <Lock size={24} color={colors.textTertiary} />
                  ) : (
                    <Clock size={24} color={colors.warning} />
                  )}
                </View>
              </View>

              <Text style={[styles.moduleTitle, module.locked && styles.moduleTitleLocked]}>
                {module.title}
              </Text>
              <Text style={[styles.moduleDescription, module.locked && styles.moduleDescriptionLocked]}>
                {module.description}
              </Text>

              {/* Points Display */}
              <View style={styles.pointsContainer}>
                <Star size={16} color={colors.warning} />
                <Text style={styles.pointsText}>{module.points} points earned</Text>
              </View>

              {/* Activities */}
              <View style={styles.activitiesContainer}>
                {Array.isArray(module.activities) && module.activities.length > 0 ? (
                  module.activities.map((activity, activityIndex) => (
                    <TouchableOpacity
                      key={activityIndex}
                      style={[
                        styles.activityChip,
                        isActivityCompleted(module.week, activity) && styles.activityChipCompleted
                      ]}
                      disabled={module.locked}
                      onPress={() => {
                        if (module.locked) return;

                        // Navigate to specific activity sections within each week
                        if (module.week === 1) {
                          if (activity === 'The Match Game') {
                            router.push('/week-one?section=0');
                          } else if (activity === 'Date Night Plan') {
                            router.push('/week-one?section=1');
                          } else if (activity === 'Chat Prompts') {
                            router.push('/week-one?section=2');
                          } else if (activity === 'Soft Start-Up') {
                            router.push('/week-one?section=3');
                          }
                        } else if (module.week === 2) {
                          if (activity === 'Strengths Bingo') {
                            router.push('/week-two?section=0');
                          } else if (activity === 'Date Night Plan') {
                            router.push('/week-two?section=1');
                          } else if (activity === 'Chat Prompts') {
                            router.push('/week-two?section=2');
                          } else if (activity === '5:1 Ratio') {
                            router.push('/week-two?section=3');
                          }
                        } else if (module.week === 3) {
                          if (activity === 'Would You Rather?') {
                            router.push('/week-three?section=0');
                          } else if (activity === 'Alphabet Date Night') {
                            router.push('/week-three?section=1');
                          } else if (activity === 'Chat Prompts') {
                            router.push('/week-three?section=2');
                          } else if (activity === 'Being Curious') {
                            router.push('/week-three?section=3');
                          }
                        } else {
                          // For weeks 4-12, navigate to the main week page
                          router.push(`/week-${module.week === 4 ? 'four' :
                                      module.week === 5 ? 'five' :
                                      module.week === 6 ? 'six' :
                                      module.week === 7 ? 'seven' :
                                      module.week === 8 ? 'eight' :
                                      module.week === 9 ? 'nine' :
                                      module.week === 10 ? 'ten' :
                                      module.week === 11 ? 'eleven' :
                                      module.week === 12 ? 'twelve' : 'one'}`);
                        }
                      }}
                    >
                      <View
                        style={[
                          styles.activityChipGradient,
                          { backgroundColor: module.locked ? colors.backgroundTertiary : module.color }
                        ]}
                      >
                        {renderActivityIcon(activity)}
                        <Text style={[
                          styles.activityChipText,
                          module.locked && styles.activityChipTextLocked
                        ]}>
                          {activity}
                        </Text>
                        {isActivityCompleted(module.week, activity) && (
                          <CheckCircle size={14} color={colors.white} style={{ marginLeft: 4 }} />
                        )}
                      </View>
                    </TouchableOpacity>
                  ))
                ) : null}
              </View>

              {!module.locked && (
                <DSButton
                  title={module.completed ? 'Review' : 'Start Module'}
                  variant="primary"
                  tone={module.completed ? 'success' : 'sage'}
                  onPress={() => {
                    if (module.week === 1) {
                      router.push('/week-one');
                    } else if (module.week === 2) {
                      router.push('/week-two');
                    } else if (module.week === 3) {
                      router.push('/week-three');
                    } else if (module.week === 4) {
                      router.push('/week-four');
                    } else if (module.week === 5) {
                      router.push('/week-five');
                    } else if (module.week === 6) {
                      router.push('/week-six');
                    } else if (module.week === 7) {
                      router.push('/week-seven');
                    } else if (module.week === 8) {
                      router.push('/week-eight');
                    } else if (module.week === 9) {
                      router.push('/week-nine');
                    } else if (module.week === 10) {
                      router.push('/week-ten');
                    } else if (module.week === 11) {
                      router.push('/week-eleven');
                    } else if (module.week === 12) {
                      router.push('/week-twelve');
                    }
                  }}
                />
              )}

              {module.locked && (
                <View style={styles.lockedMessage}>
                  <Text style={styles.lockedText}>
                    Complete previous modules to unlock
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ))
        ) : null}

        <View style={styles.completionCard}>
          <View
            style={[styles.completionGradient, { backgroundColor: colors.backgroundOrange }]}
          >
            <Heart size={32} color={colors.primary} />
            <Text style={styles.completionTitle}>Journey Completion</Text>
            <Text style={styles.completionText}>
              After completing all 12 weeks, unlock your personalized Relationship Yearbook -
              a beautiful keepsake of your growth together.
            </Text>
            <View style={styles.totalPointsDisplay}>
              <Star size={20} color={colors.warning} />
              <Text style={styles.totalPointsText}>
                Total Points: {totalPoints}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundPrimary,
  },
  headerSubtitleContainer: {
    backgroundColor: colors.primary,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.textInverse,
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  moduleCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  moduleCardLocked: {
    opacity: 0.6,
  },
  moduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  moduleWeek: {
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  moduleWeekText: {
    color: colors.textInverse,
    fontSize: 12,
    fontWeight: '600',
  },
  moduleStatus: {
    // Icon container
  },
  moduleTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  moduleTitleLocked: {
    color: colors.textTertiary,
  },
  moduleDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  moduleDescriptionLocked: {
    color: colors.borderMedium,
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  pointsText: {
    color: colors.warning,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  activitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  activityChip: {
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 16,
    overflow: 'hidden',
  },
  activityChipCompleted: {
    opacity: 0.6,
  },
  activityChipGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  activityChipText: {
    color: colors.textInverse,
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  activityChipTextLocked: {
    color: colors.textTertiary,
  },
  startButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  startButtonGradient: {
    padding: 14,
    alignItems: 'center',
  },
  startButtonText: {
    color: colors.textInverse,
    fontSize: 16,
    fontWeight: '600',
  },
  lockedMessage: {
    backgroundColor: colors.backgroundGray,
    padding: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  lockedText: {
    color: colors.textTertiary,
    fontSize: 14,
    fontWeight: '500',
  },
  completionCard: {
    marginBottom: 40,
    borderRadius: 16,
    overflow: 'hidden',
  },
  completionGradient: {
    padding: 24,
    alignItems: 'center',
  },
  completionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary,
    marginTop: 12,
    marginBottom: 8,
  },
  completionText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  totalPointsDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  totalPointsText: {
    color: colors.warning,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
});
