import { router, useLocalSearchParams } from 'expo-router';
import React from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';
import {
    <PERSON>Footer,
    StoryFooterCard,
    StoryHeader,
    StoryIntroCard,
    StoryTimelineItem
} from '../src/components/shared';
import { useOriginStoryData } from '../src/hooks/useOriginStoryData';
import { getDefaultStorySections } from '../src/shared/components/common/StoryData';
import { colors } from '../src/shared/utils/colors';
import { logger } from '../src/shared/utils/logger';
import { sanitizeText } from '../src/utils/validation';

export default function OurStoryScreen() {
  const params = useLocalSearchParams();
  const isEditMode = params.mode === 'edit';

  const {
    data: storyData,
    updateField,
    addPhoto,
    removePhoto,
    saveData,
    getCompletionPercentage,
    isLoading
  } = useOriginStoryData();

  const handleSkip = () => {
    router.push('/(tabs)');
  };

  const handleSave = async () => {
    try {
      const validatedData = { ...storyData };

      for (const [key, value] of Object.entries(storyData)) {
        if (key === 'completedSections' || key === 'lastUpdated' || key.includes('Photos')) continue;

        if (typeof value === 'string' && value && value.trim() !== '') {
          const validation = sanitizeText(value, 2000);
          if (!validation.isValid) {
            return;
          }
          (validatedData as any)[key] = validation.sanitizedValue!;
        }
      }

      await saveData(validatedData);
      logger.info('Origin story saved successfully', { fieldCount: Object.keys(validatedData).length });

      // Show success toast
      (await import('../utils/toast')).showToast('Story saved successfully');

      router.push('/(tabs)');
    } catch (error) {
      logger.error('Error saving origin story:', error);
      (await import('../utils/toast')).showToast('Failed to save story');
    }
  };

  const handleBack = () => {
    router.back();
  };

  const handleEdit = () => {
    router.push('/our-story?mode=edit');
  };

  const handleFieldUpdate = async (field: any, value: string) => {
    try {
      if (value.length > 2000) {
        return;
      }
      await updateField(field as keyof typeof storyData, value);
    } catch (error) {
      logger.error('Error updating field:', error);
    }
  };

  const storyTimeline = getDefaultStorySections(storyData);

  const renderEditMode = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <StoryIntroCard
        text="Before we begin, let's start with what makes you two 'you'. Take a moment to reflect on your journey together. How did you find each other? What sparks ignited your connection, and how has your love story evolved?"
      />

      <View style={styles.formCard}>
        {storyTimeline.map((story: any) => (
          <StoryTimelineItem
            key={story.id}
            section={story}
            index={storyTimeline.indexOf(story)}
            totalSections={storyTimeline.length}
            mode="edit"
            onFieldUpdate={handleFieldUpdate}
            onAddPhoto={addPhoto}
            onRemovePhoto={removePhoto}
          />
        ))}
      </View>

      <StoryFooter onSkip={handleSkip} onSave={handleSave} />
    </ScrollView>
  );

  const renderViewMode = () => (
    <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
      <StoryIntroCard
        title="Prelude: Our Love Story"
        text="Every love story is unique, and ours is no exception. Here's the journey that brought us together and continues to shape our relationship every day."
      />

      <View style={styles.timelineContainer}>
        {storyTimeline.map((story: any, index: number) => (
          <StoryTimelineItem
            key={story.id}
            section={story}
            index={index}
            totalSections={storyTimeline.length}
            mode="view"
          />
        ))}
      </View>

      <StoryFooterCard />
    </ScrollView>
  );

  if (isLoading) {
    return (
      <View style={styles.container}>
        <StoryHeader
          title="Our Origin Story"
          onBack={handleBack}
          onEdit={!isEditMode ? handleEdit : undefined}
          isEditMode={isEditMode}
        />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading your story...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StoryHeader
        title={isEditMode ? 'Prelude: Our Origin Story' : 'Our Origin Story'}
        onBack={handleBack}
        onEdit={!isEditMode ? handleEdit : undefined}
        onView={isEditMode ? handleEdit : undefined}
        isEditMode={isEditMode}
        showProgress={isEditMode}
        progressPercentage={getCompletionPercentage()}
      />

      {isEditMode ? renderEditMode() : renderViewMode()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundPrimary,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  formCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  timelineContainer: {
    marginBottom: 24,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});
