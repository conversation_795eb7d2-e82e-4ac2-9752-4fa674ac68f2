/**
 * Favorites Context
 * Manages user favorites across the app
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface FavoriteItem {
  id: string;
  type: 'date_idea' | 'ritual' | 'memory' | 'photo' | 'activity';
  title: string;
  data: any;
  addedAt: Date;
}

export interface FavoritesContextType {
  favorites: FavoriteItem[];
  addToFavorites: (item: Omit<FavoriteItem, 'addedAt'>) => void;
  removeFromFavorites: (id: string) => void;
  isFavorite: (id: string) => boolean;
  getFavoritesByType: (type: FavoriteItem['type']) => FavoriteItem[];
  clearFavorites: () => void;
  favoritesCount: number;
}

const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined);

export interface FavoritesProviderProps {
  children: ReactNode;
}

export function FavoritesProvider({ children }: FavoritesProviderProps) {
  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);

  // Load favorites from storage on mount
  useEffect(() => {
    loadFavorites();
  }, []);

  // Save favorites to storage whenever they change
  useEffect(() => {
    saveFavorites();
  }, [favorites]);

  const loadFavorites = async () => {
    try {
      // In a real app, this would load from AsyncStorage or similar
      const stored = localStorage.getItem('user_favorites');
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert date strings back to Date objects
        const favoritesWithDates = parsed.map((item: any) => ({
          ...item,
          addedAt: new Date(item.addedAt)
        }));
        setFavorites(favoritesWithDates);
      }
    } catch (error) {
      console.error('Failed to load favorites:', error);
    }
  };

  const saveFavorites = async () => {
    try {
      // In a real app, this would save to AsyncStorage or similar
      localStorage.setItem('user_favorites', JSON.stringify(favorites));
    } catch (error) {
      console.error('Failed to save favorites:', error);
    }
  };

  const addToFavorites = (item: Omit<FavoriteItem, 'addedAt'>) => {
    const newFavorite: FavoriteItem = {
      ...item,
      addedAt: new Date()
    };

    setFavorites(prev => {
      // Check if already exists
      const exists = prev.some(fav => fav.id === item.id);
      if (exists) return prev;
      
      return [...prev, newFavorite];
    });
  };

  const removeFromFavorites = (id: string) => {
    setFavorites(prev => prev.filter(fav => fav.id !== id));
  };

  const isFavorite = (id: string): boolean => {
    return favorites.some(fav => fav.id === id);
  };

  const getFavoritesByType = (type: FavoriteItem['type']): FavoriteItem[] => {
    return favorites
      .filter(fav => fav.type === type)
      .sort((a, b) => b.addedAt.getTime() - a.addedAt.getTime());
  };

  const clearFavorites = () => {
    setFavorites([]);
  };

  const value: FavoritesContextType = {
    favorites,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    getFavoritesByType,
    clearFavorites,
    favoritesCount: favorites.length
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
}

export function useFavorites(): FavoritesContextType {
  const context = useContext(FavoritesContext);
  if (context === undefined) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
}

// Hook for specific favorite type
export function useFavoritesByType(type: FavoriteItem['type']) {
  const { getFavoritesByType } = useFavorites();
  return getFavoritesByType(type);
}

// Hook for checking if item is favorite
export function useIsFavorite(id: string) {
  const { isFavorite } = useFavorites();
  return isFavorite(id);
}

// Hook for toggling favorite status
export function useToggleFavorite() {
  const { addToFavorites, removeFromFavorites, isFavorite } = useFavorites();
  
  return (item: Omit<FavoriteItem, 'addedAt'>) => {
    if (isFavorite(item.id)) {
      removeFromFavorites(item.id);
    } else {
      addToFavorites(item);
    }
  };
}
