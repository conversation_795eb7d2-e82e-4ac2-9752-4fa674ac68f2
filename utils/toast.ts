/**
 * Toast Utility
 * Simple toast notification system
 */

import React from 'react';

export interface ToastOptions {
  duration?: number;
  position?: 'top' | 'bottom' | 'center';
  type?: 'success' | 'error' | 'warning' | 'info';
  action?: {
    label: string;
    onPress: () => void;
  };
}

export interface Toast {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration: number;
  position: 'top' | 'bottom' | 'center';
  action?: {
    label: string;
    onPress: () => void;
  };
  createdAt: Date;
}

class ToastManager {
  private toasts: Toast[] = [];
  private listeners: ((toasts: Toast[]) => void)[] = [];

  private generateId(): string {
    return `toast_${Date.now()}_${Math.random().toString(36).substring(7)}`;
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener([...this.toasts]));
  }

  show(message: string, options: ToastOptions = {}): string {
    const {
      duration = 3000,
      position = 'bottom',
      type = 'info',
      action
    } = options;

    const toast: Toast = {
      id: this.generateId(),
      message,
      type,
      duration,
      position,
      action,
      createdAt: new Date()
    };

    this.toasts.push(toast);
    this.notifyListeners();

    // Auto-dismiss after duration
    if (duration > 0) {
      setTimeout(() => {
        this.dismiss(toast.id);
      }, duration);
    }

    return toast.id;
  }

  success(message: string, options: Omit<ToastOptions, 'type'> = {}): string {
    return this.show(message, { ...options, type: 'success' });
  }

  error(message: string, options: Omit<ToastOptions, 'type'> = {}): string {
    return this.show(message, { ...options, type: 'error', duration: options.duration || 5000 });
  }

  warning(message: string, options: Omit<ToastOptions, 'type'> = {}): string {
    return this.show(message, { ...options, type: 'warning', duration: options.duration || 4000 });
  }

  info(message: string, options: Omit<ToastOptions, 'type'> = {}): string {
    return this.show(message, { ...options, type: 'info' });
  }

  dismiss(toastId: string): void {
    this.toasts = this.toasts.filter(toast => toast.id !== toastId);
    this.notifyListeners();
  }

  dismissAll(): void {
    this.toasts = [];
    this.notifyListeners();
  }

  getToasts(): Toast[] {
    return [...this.toasts];
  }

  subscribe(listener: (toasts: Toast[]) => void): () => void {
    this.listeners.push(listener);

    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // Get toasts by position for rendering
  getToastsByPosition(position: 'top' | 'bottom' | 'center'): Toast[] {
    return this.toasts.filter(toast => toast.position === position);
  }

  // Clear expired toasts
  clearExpired(): void {
    const now = new Date();
    this.toasts = this.toasts.filter(toast => {
      if (toast.duration <= 0) return true; // Keep persistent toasts
      const elapsed = now.getTime() - toast.createdAt.getTime();
      return elapsed < toast.duration;
    });
    this.notifyListeners();
  }
}

// Singleton instance
const toastManager = new ToastManager();

// Export convenience functions
export const toast = {
  show: (message: string, options?: ToastOptions) => toastManager.show(message, options),
  success: (message: string, options?: Omit<ToastOptions, 'type'>) => toastManager.success(message, options),
  error: (message: string, options?: Omit<ToastOptions, 'type'>) => toastManager.error(message, options),
  warning: (message: string, options?: Omit<ToastOptions, 'type'>) => toastManager.warning(message, options),
  info: (message: string, options?: Omit<ToastOptions, 'type'>) => toastManager.info(message, options),
  dismiss: (toastId: string) => toastManager.dismiss(toastId),
  dismissAll: () => toastManager.dismissAll(),
  subscribe: (listener: (toasts: Toast[]) => void) => toastManager.subscribe(listener),
  getToasts: () => toastManager.getToasts(),
  getToastsByPosition: (position: 'top' | 'bottom' | 'center') => toastManager.getToastsByPosition(position),
  clearExpired: () => toastManager.clearExpired()
};

// React hook for using toasts
export function useToast() {
  const [toasts, setToasts] = React.useState<Toast[]>([]);

  React.useEffect(() => {
    const unsubscribe = toastManager.subscribe(setToasts);
    setToasts(toastManager.getToasts()); // Initial load
    return unsubscribe;
  }, []);

  return {
    toasts,
    show: toast.show,
    success: toast.success,
    error: toast.error,
    warning: toast.warning,
    info: toast.info,
    dismiss: toast.dismiss,
    dismissAll: toast.dismissAll,
    getToastsByPosition: toast.getToastsByPosition
  };
}

// Default export for backward compatibility
export default toast;

// Additional utility functions
export const showSuccessToast = (message: string) => toast.success(message);
export const showErrorToast = (message: string) => toast.error(message);
export const showWarningToast = (message: string) => toast.warning(message);
export const showInfoToast = (message: string) => toast.info(message);

// Toast component props for UI libraries
export interface ToastComponentProps {
  toast: Toast;
  onDismiss: (id: string) => void;
}

// Predefined messages
export const TOAST_MESSAGES = {
  SUCCESS: {
    SAVED: 'Changes saved successfully!',
    DELETED: 'Item deleted successfully!',
    CREATED: 'Item created successfully!',
    UPDATED: 'Item updated successfully!',
    CONNECTED: 'Connected successfully!',
    SENT: 'Message sent successfully!'
  },
  ERROR: {
    GENERIC: 'Something went wrong. Please try again.',
    NETWORK: 'Network error. Please check your connection.',
    VALIDATION: 'Please check your input and try again.',
    PERMISSION: 'You do not have permission to perform this action.',
    NOT_FOUND: 'The requested item was not found.',
    SERVER: 'Server error. Please try again later.'
  },
  WARNING: {
    UNSAVED_CHANGES: 'You have unsaved changes.',
    LOW_STORAGE: 'Storage space is running low.',
    SLOW_CONNECTION: 'Your connection seems slow.',
    BETA_FEATURE: 'This is a beta feature.'
  },
  INFO: {
    LOADING: 'Loading...',
    SYNCING: 'Syncing data...',
    OFFLINE: 'You are currently offline.',
    MAINTENANCE: 'Scheduled maintenance in progress.'
  }
} as const;
