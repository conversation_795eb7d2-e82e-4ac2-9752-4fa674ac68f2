/**
 * Validation Engine
 * Comprehensive data validation and sanitization
 */

export enum ValidationType {
  EMAIL = 'email',
  PASSWORD = 'password',
  STRING = 'string',
  NUMBER = 'number',
  INTEGER = 'integer',
  BOOLEAN = 'boolean',
  DATE = 'date',
  URL = 'url',
  PHONE = 'phone',
  ARRAY = 'array',
  OBJECT = 'object'
}

export interface ValidationRule {
  type: ValidationType;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  min?: number;
  max?: number;
  custom?: (value: any) => boolean | string;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  sanitizedValue?: any;
}

export interface ValidationError {
  field: string;
  message: string;
  type: ValidationType;
  code: string;
  value: any;
}

class ValidationEngine {
  private emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  private phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  private urlRegex = /^https?:\/\/.+/;

  validateObject(data: any, schema: ValidationSchema): ValidationResult {
    const errors: ValidationError[] = [];
    const sanitizedValue: any = {};

    for (const [field, rule] of Object.entries(schema)) {
      const value = data[field];
      const fieldResult = this.validateField(field, value, rule);

      if (!fieldResult.isValid) {
        errors.push(...fieldResult.errors);
      } else if (fieldResult.sanitizedValue !== undefined) {
        sanitizedValue[field] = fieldResult.sanitizedValue;
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: errors.length === 0 ? sanitizedValue : undefined
    };
  }

  private validateField(field: string, value: any, rule: ValidationRule): ValidationResult {
    const errors: ValidationError[] = [];

    // Check required
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push({
        field,
        message: `${field} is required`,
        type: rule.type,
        code: 'REQUIRED',
        value
      });
      return { isValid: false, errors };
    }

    // Skip validation if value is empty and not required
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return { isValid: true, errors: [], sanitizedValue: value };
    }

    // Type-specific validation
    let sanitizedValue = value;

    switch (rule.type) {
      case ValidationType.EMAIL:
        if (!this.emailRegex.test(value)) {
          errors.push({
            field,
            message: `${field} must be a valid email address`,
            type: rule.type,
            code: 'INVALID_EMAIL',
            value
          });
        } else {
          sanitizedValue = value.toLowerCase().trim();
        }
        break;

      case ValidationType.PASSWORD:
        if (typeof value !== 'string' || value.length < (rule.minLength || 8)) {
          errors.push({
            field,
            message: `${field} must be at least ${rule.minLength || 8} characters long`,
            type: rule.type,
            code: 'INVALID_PASSWORD',
            value
          });
        }
        break;

      case ValidationType.STRING:
        if (typeof value !== 'string') {
          errors.push({
            field,
            message: `${field} must be a string`,
            type: rule.type,
            code: 'INVALID_STRING',
            value
          });
        } else {
          sanitizedValue = value.trim();

          if (rule.minLength && sanitizedValue.length < rule.minLength) {
            errors.push({
              field,
              message: `${field} must be at least ${rule.minLength} characters long`,
              type: rule.type,
              code: 'STRING_TOO_SHORT',
              value
            });
          }

          if (rule.maxLength && sanitizedValue.length > rule.maxLength) {
            errors.push({
              field,
              message: `${field} must be no more than ${rule.maxLength} characters long`,
              type: rule.type,
              code: 'STRING_TOO_LONG',
              value
            });
          }
        }
        break;

      case ValidationType.NUMBER:
        const numValue = Number(value);
        if (isNaN(numValue)) {
          errors.push({
            field,
            message: `${field} must be a valid number`,
            type: rule.type,
            code: 'INVALID_NUMBER',
            value
          });
        } else {
          sanitizedValue = numValue;

          if (rule.min !== undefined && numValue < rule.min) {
            errors.push({
              field,
              message: `${field} must be at least ${rule.min}`,
              type: rule.type,
              code: 'NUMBER_TOO_SMALL',
              value
            });
          }

          if (rule.max !== undefined && numValue > rule.max) {
            errors.push({
              field,
              message: `${field} must be no more than ${rule.max}`,
              type: rule.type,
              code: 'NUMBER_TOO_LARGE',
              value
            });
          }
        }
        break;

      case ValidationType.URL:
        if (!this.urlRegex.test(value)) {
          errors.push({
            field,
            message: `${field} must be a valid URL`,
            type: rule.type,
            code: 'INVALID_URL',
            value
          });
        }
        break;

      case ValidationType.PHONE:
        if (!this.phoneRegex.test(value)) {
          errors.push({
            field,
            message: `${field} must be a valid phone number`,
            type: rule.type,
            code: 'INVALID_PHONE',
            value
          });
        }
        break;

      case ValidationType.INTEGER:
        const intValue = parseInt(value, 10);
        if (isNaN(intValue) || intValue !== Number(value)) {
          errors.push({
            field,
            message: `${field} must be a valid integer`,
            type: rule.type,
            code: 'INVALID_INTEGER',
            value
          });
        } else {
          sanitizedValue = intValue;

          if (rule.min !== undefined && intValue < rule.min) {
            errors.push({
              field,
              message: `${field} must be at least ${rule.min}`,
              type: rule.type,
              code: 'INTEGER_TOO_SMALL',
              value
            });
          }

          if (rule.max !== undefined && intValue > rule.max) {
            errors.push({
              field,
              message: `${field} must be no more than ${rule.max}`,
              type: rule.type,
              code: 'INTEGER_TOO_LARGE',
              value
            });
          }
        }
        break;

      case ValidationType.ARRAY:
        if (!Array.isArray(value)) {
          errors.push({
            field,
            message: `${field} must be an array`,
            type: rule.type,
            code: 'INVALID_ARRAY',
            value
          });
        } else {
          sanitizedValue = value;

          if (rule.minLength && value.length < rule.minLength) {
            errors.push({
              field,
              message: `${field} must have at least ${rule.minLength} items`,
              type: rule.type,
              code: 'ARRAY_TOO_SHORT',
              value
            });
          }

          if (rule.maxLength && value.length > rule.maxLength) {
            errors.push({
              field,
              message: `${field} must have no more than ${rule.maxLength} items`,
              type: rule.type,
              code: 'ARRAY_TOO_LONG',
              value
            });
          }
        }
        break;

      case ValidationType.OBJECT:
        if (typeof value !== 'object' || value === null || Array.isArray(value)) {
          errors.push({
            field,
            message: `${field} must be a valid object`,
            type: rule.type,
            code: 'INVALID_OBJECT',
            value
          });
        } else {
          sanitizedValue = value;
        }
        break;
    }

    // Custom validation
    if (rule.custom && errors.length === 0) {
      const customResult = rule.custom(sanitizedValue);
      if (customResult !== true) {
        errors.push({
          field,
          message: typeof customResult === 'string' ? customResult : `${field} failed custom validation`,
          type: rule.type,
          code: 'CUSTOM_VALIDATION_FAILED',
          value
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: errors.length === 0 ? sanitizedValue : undefined
    };
  }

  registerSanitizer(type: ValidationType, sanitizer: (value: any) => any): void {
    // Store custom sanitizers for different validation types
    // This would be implemented with a Map or similar storage
    console.log(`Registered sanitizer for type: ${type}`);
  }
}

// Utility functions
export function validateEmail(email: string): ValidationResult {
  const engine = new ValidationEngine();
  return engine.validateObject({ email }, {
    email: { type: ValidationType.EMAIL, required: true }
  });
}

export function validatePassword(password: string, minLength: number = 8): ValidationResult {
  const engine = new ValidationEngine();
  return engine.validateObject({ password }, {
    password: { type: ValidationType.PASSWORD, required: true, minLength }
  });
}

// Singleton instance
export const validationEngine = new ValidationEngine();
