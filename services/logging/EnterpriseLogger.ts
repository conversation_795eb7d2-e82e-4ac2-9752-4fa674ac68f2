/**
 * Enterprise Logging System
 * Advanced logging with analytics and monitoring
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  message: string;
  category: string;
  userId?: string;
  sessionId?: string;
  metadata?: any;
  stack?: string;
}

export interface LogStats {
  totalLogs: number;
  errorCount: number;
  warnCount: number;
  infoCount: number;
  debugCount: number;
  lastLog?: LogEntry;
}

export interface UserAction {
  action: string;
  screen: string;
  userId?: string;
  timestamp: Date;
  metadata?: any;
}

class EnterpriseLogger {
  private logs: LogEntry[] = [];
  private userActions: UserAction[] = [];
  private currentLogLevel: LogLevel = LogLevel.INFO;
  private maxLogEntries: number = 1000;
  private stats: LogStats = {
    totalLogs: 0,
    errorCount: 0,
    warnCount: 0,
    infoCount: 0,
    debugCount: 0
  };

  setLogLevel(level: LogLevel): void {
    this.currentLogLevel = level;
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    category: string = 'general',
    metadata?: any,
    error?: Error
  ): LogEntry {
    return {
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      level,
      message,
      category,
      metadata,
      stack: error?.stack
    };
  }

  private addLog(entry: LogEntry): void {
    if (entry.level < this.currentLogLevel) return;

    this.logs.push(entry);
    this.updateStats(entry);

    // Maintain max log entries
    if (this.logs.length > this.maxLogEntries) {
      this.logs.shift();
    }

    // Console output
    const levelName = LogLevel[entry.level];
    const timestamp = entry.timestamp.toISOString();
    console.log(`[${timestamp}] ${levelName}: ${entry.message}`, entry.metadata || '');
  }

  private updateStats(entry: LogEntry): void {
    this.stats.totalLogs++;
    this.stats.lastLog = entry;

    switch (entry.level) {
      case LogLevel.DEBUG:
        this.stats.debugCount++;
        break;
      case LogLevel.INFO:
        this.stats.infoCount++;
        break;
      case LogLevel.WARN:
        this.stats.warnCount++;
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        this.stats.errorCount++;
        break;
    }
  }

  debug(message: string, metadata?: any): void {
    const entry = this.createLogEntry(LogLevel.DEBUG, message, 'debug', metadata);
    this.addLog(entry);
  }

  info(message: string, metadata?: any): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, 'info', metadata);
    this.addLog(entry);
  }

  warn(message: string, metadata?: any): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, 'warning', metadata);
    this.addLog(entry);
  }

  error(message: string, error?: Error, metadata?: any): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, 'error', metadata, error);
    this.addLog(entry);
  }

  fatal(message: string, error?: Error, metadata?: any): void {
    const entry = this.createLogEntry(LogLevel.FATAL, message, 'fatal', metadata, error);
    this.addLog(entry);
  }

  logUserAction(action: string, screen: string, userId?: string, metadata?: any): void {
    const userAction: UserAction = {
      action,
      screen,
      userId,
      timestamp: new Date(),
      metadata
    };

    this.userActions.push(userAction);

    // Also log as info
    this.info(`User action: ${action} on ${screen}`, { userId, ...metadata });

    // Maintain max user actions
    if (this.userActions.length > this.maxLogEntries) {
      this.userActions.shift();
    }
  }

  getStats(): LogStats & { logsByLevel: { [key: string]: number } } {
    const logsByLevel: { [key: string]: number } = {
      DEBUG: this.stats.debugCount,
      INFO: this.stats.infoCount,
      WARN: this.stats.warnCount,
      ERROR: this.stats.errorCount
    };

    return {
      ...this.stats,
      logsByLevel
    };
  }

  getLogs(level?: LogLevel, limit: number = 50): LogEntry[] {
    let filteredLogs = this.logs;

    if (level !== undefined) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }

    return filteredLogs.slice(-limit);
  }

  getUserActions(limit: number = 50): UserAction[] {
    return this.userActions.slice(-limit);
  }

  searchLogs(query: string, limit: number = 50): LogEntry[] {
    const searchTerm = query.toLowerCase();
    return this.logs
      .filter(log =>
        log.message.toLowerCase().includes(searchTerm) ||
        log.category.toLowerCase().includes(searchTerm)
      )
      .slice(-limit);
  }

  clearLogs(): void {
    this.logs = [];
    this.userActions = [];
    this.stats = {
      totalLogs: 0,
      errorCount: 0,
      warnCount: 0,
      infoCount: 0,
      debugCount: 0
    };
  }

  exportLogs(): string {
    return JSON.stringify({
      logs: this.logs,
      userActions: this.userActions,
      stats: this.stats,
      exportedAt: new Date().toISOString()
    }, null, 2);
  }
}

// Utility functions
export function info(message: string, metadata?: any): void {
  enterpriseLogger.info(message, metadata);
}

export function error(message: string, errorObj?: Error, metadata?: any): void {
  enterpriseLogger.error(message, errorObj, metadata);
}

export function logUserAction(action: string, screen: string, userId?: string, metadata?: any): void {
  enterpriseLogger.logUserAction(action, screen, userId, metadata);
}

// Singleton instance
export const enterpriseLogger = new EnterpriseLogger();
