/**
 * Image Storage Service
 * Handles image upload, storage, and retrieval
 */

export interface ImageUploadOptions {
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  format?: 'jpeg' | 'png' | 'webp';
  folder?: string;
}

export interface StoredImage {
  id: string;
  url: string;
  thumbnailUrl?: string;
  originalName: string;
  size: number;
  mimeType: string;
  width?: number;
  height?: number;
  uploadedAt: Date;
  folder?: string;
}

export interface ImageUploadResult {
  success: boolean;
  image?: StoredImage;
  error?: string;
}

export interface ImageStorageStats {
  totalImages: number;
  totalSize: number;
  averageSize: number;
  imagesByFolder: { [folder: string]: number };
  recentUploads: StoredImage[];
}

class ImageStorageService {
  private images: Map<string, StoredImage> = new Map();
  private folderStats: Map<string, number> = new Map();

  async uploadImage(
    imageData: string | File, 
    options: ImageUploadOptions = {}
  ): Promise<ImageUploadResult> {
    try {
      // Simulate image processing
      const processedImage = await this.processImage(imageData, options);
      
      // Generate unique ID
      const imageId = this.generateImageId();
      
      // Create stored image record
      const storedImage: StoredImage = {
        id: imageId,
        url: `https://storage.example.com/images/${options.folder || 'default'}/${imageId}.${options.format || 'jpeg'}`,
        thumbnailUrl: `https://storage.example.com/thumbnails/${options.folder || 'default'}/${imageId}_thumb.${options.format || 'jpeg'}`,
        originalName: typeof imageData === 'string' ? 'base64_image' : imageData.name,
        size: this.calculateImageSize(imageData),
        mimeType: `image/${options.format || 'jpeg'}`,
        width: options.maxWidth || 1920,
        height: options.maxHeight || 1080,
        uploadedAt: new Date(),
        folder: options.folder || 'default'
      };

      // Store the image
      this.images.set(imageId, storedImage);
      
      // Update folder stats
      const folder = storedImage.folder!;
      this.folderStats.set(folder, (this.folderStats.get(folder) || 0) + 1);

      return {
        success: true,
        image: storedImage
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async getImage(imageId: string): Promise<StoredImage | null> {
    return this.images.get(imageId) || null;
  }

  async getImagesByFolder(folder: string): Promise<StoredImage[]> {
    return Array.from(this.images.values())
      .filter(image => image.folder === folder)
      .sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime());
  }

  async deleteImage(imageId: string): Promise<boolean> {
    const image = this.images.get(imageId);
    if (!image) return false;

    // Update folder stats
    const folder = image.folder!;
    const currentCount = this.folderStats.get(folder) || 0;
    if (currentCount > 1) {
      this.folderStats.set(folder, currentCount - 1);
    } else {
      this.folderStats.delete(folder);
    }

    // Remove image
    this.images.delete(imageId);
    return true;
  }

  async searchImages(query: string): Promise<StoredImage[]> {
    const searchTerm = query.toLowerCase();
    return Array.from(this.images.values())
      .filter(image => 
        image.originalName.toLowerCase().includes(searchTerm) ||
        (image.folder && image.folder.toLowerCase().includes(searchTerm))
      )
      .sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime());
  }

  async getRecentImages(limit: number = 20): Promise<StoredImage[]> {
    return Array.from(this.images.values())
      .sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime())
      .slice(0, limit);
  }

  getStorageStats(): ImageStorageStats {
    const images = Array.from(this.images.values());
    const totalImages = images.length;
    const totalSize = images.reduce((sum, img) => sum + img.size, 0);
    const averageSize = totalImages > 0 ? totalSize / totalImages : 0;

    const imagesByFolder: { [folder: string]: number } = {};
    this.folderStats.forEach((count, folder) => {
      imagesByFolder[folder] = count;
    });

    const recentUploads = images
      .sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime())
      .slice(0, 10);

    return {
      totalImages,
      totalSize,
      averageSize,
      imagesByFolder,
      recentUploads
    };
  }

  private async processImage(
    imageData: string | File, 
    options: ImageUploadOptions
  ): Promise<void> {
    // Simulate image processing (resize, compress, etc.)
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // In a real implementation, this would:
    // - Resize the image if needed
    // - Compress based on quality setting
    // - Convert format if specified
    // - Generate thumbnail
    // - Upload to cloud storage
  }

  private generateImageId(): string {
    return `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateImageSize(imageData: string | File): number {
    if (typeof imageData === 'string') {
      // Base64 string size estimation
      return Math.round((imageData.length * 3) / 4);
    } else {
      return imageData.size;
    }
  }

  async createThumbnail(imageId: string, width: number = 150, height: number = 150): Promise<string | null> {
    const image = this.images.get(imageId);
    if (!image) return null;

    // Simulate thumbnail creation
    const thumbnailUrl = `https://storage.example.com/thumbnails/${image.folder}/${imageId}_${width}x${height}.jpeg`;
    
    // Update the image record with thumbnail URL
    image.thumbnailUrl = thumbnailUrl;
    this.images.set(imageId, image);

    return thumbnailUrl;
  }

  async optimizeImage(imageId: string): Promise<boolean> {
    const image = this.images.get(imageId);
    if (!image) return false;

    // Simulate image optimization
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Reduce file size by ~30% (simulation)
    image.size = Math.round(image.size * 0.7);
    this.images.set(imageId, image);

    return true;
  }

  getFolders(): string[] {
    return Array.from(this.folderStats.keys()).sort();
  }

  async bulkDelete(imageIds: string[]): Promise<{ deleted: number; failed: string[] }> {
    let deleted = 0;
    const failed: string[] = [];

    for (const imageId of imageIds) {
      const success = await this.deleteImage(imageId);
      if (success) {
        deleted++;
      } else {
        failed.push(imageId);
      }
    }

    return { deleted, failed };
  }

  clearStorage(): void {
    this.images.clear();
    this.folderStats.clear();
  }
}

// Singleton instance
export const imageStorageService = new ImageStorageService();
