/**
 * Authentication Service
 * Handles user authentication and session management
 */

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  partnerId?: string;
  createdAt: Date;
  lastLoginAt: Date;
}

export interface AuthSession {
  user: User;
  token: string;
  expiresAt: Date;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
}

export interface AuthError {
  code: string;
  message: string;
}

class AuthService {
  private currentUser: User | null = null;
  private currentSession: AuthSession | null = null;

  async login(credentials: LoginCredentials): Promise<AuthSession> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock user data
      const user: User = {
        id: Math.random().toString(36).substr(2, 9),
        email: credentials.email,
        name: 'User Name',
        createdAt: new Date(),
        lastLoginAt: new Date()
      };

      const session: AuthSession = {
        user,
        token: `token_${Math.random().toString(36).substr(2, 20)}`,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      };

      this.currentUser = user;
      this.currentSession = session;

      return session;
    } catch (error) {
      throw new Error('Login failed');
    }
  }

  async register(data: RegisterData): Promise<AuthSession> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      const user: User = {
        id: Math.random().toString(36).substr(2, 9),
        email: data.email,
        name: data.name,
        createdAt: new Date(),
        lastLoginAt: new Date()
      };

      const session: AuthSession = {
        user,
        token: `token_${Math.random().toString(36).substr(2, 20)}`,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      };

      this.currentUser = user;
      this.currentSession = session;

      return session;
    } catch (error) {
      throw new Error('Registration failed');
    }
  }

  async logout(): Promise<void> {
    this.currentUser = null;
    this.currentSession = null;
  }

  getCurrentUser(): User | null {
    return this.currentUser;
  }

  getCurrentSession(): AuthSession | null {
    return this.currentSession;
  }

  isAuthenticated(): boolean {
    return this.currentSession !== null && 
           this.currentSession.expiresAt > new Date();
  }

  async refreshToken(): Promise<string> {
    if (!this.currentSession) {
      throw new Error('No active session');
    }

    // Simulate token refresh
    const newToken = `token_${Math.random().toString(36).substr(2, 20)}`;
    this.currentSession.token = newToken;
    this.currentSession.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);

    return newToken;
  }

  async resetPassword(email: string): Promise<void> {
    // Simulate password reset
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log(`Password reset email sent to ${email}`);
  }

  async updateProfile(updates: Partial<User>): Promise<User> {
    if (!this.currentUser) {
      throw new Error('No authenticated user');
    }

    this.currentUser = { ...this.currentUser, ...updates };
    return this.currentUser;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    if (!this.currentUser) {
      throw new Error('No authenticated user');
    }

    // Simulate password change
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('Password changed successfully');
  }

  async deleteAccount(): Promise<void> {
    if (!this.currentUser) {
      throw new Error('No authenticated user');
    }

    // Simulate account deletion
    await new Promise(resolve => setTimeout(resolve, 1500));
    this.currentUser = null;
    this.currentSession = null;
  }
}

// Singleton instance
export const authService = new AuthService();

// Convenience functions
export const login = (credentials: LoginCredentials) => authService.login(credentials);
export const register = (data: RegisterData) => authService.register(data);
export const logout = () => authService.logout();
export const getCurrentUser = () => authService.getCurrentUser();
export const isAuthenticated = () => authService.isAuthenticated();
