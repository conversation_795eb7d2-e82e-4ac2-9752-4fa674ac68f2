/**
 * Error Recovery Service
 * Handles automatic error recovery and retry logic
 */

import { AppError } from '../errorHandling/ErrorManager';

export interface RecoveryStrategy {
  name: string;
  canRecover: (error: AppError) => boolean;
  recover: (error: AppError) => Promise<boolean>;
  maxAttempts: number;
}

export interface RecoveryStats {
  totalRecoveries: number;
  successfulRecoveries: number;
  failedRecoveries: number;
  successRate: number;
  lastRecovery?: Date;
}

export interface RecoveryAttempt {
  error: AppError;
  strategy: string;
  attempts: number;
  success: boolean;
  timestamp: Date;
}

class ErrorRecoveryService {
  private strategies: RecoveryStrategy[] = [];
  private recoveryHistory: RecoveryAttempt[] = [];
  private stats: RecoveryStats = {
    totalRecoveries: 0,
    successfulRecoveries: 0,
    failedRecoveries: 0,
    successRate: 0
  };

  constructor() {
    this.initializeDefaultStrategies();
  }

  private initializeDefaultStrategies(): void {
    // Network error recovery
    this.strategies.push({
      name: 'NetworkRetry',
      canRecover: (error) => error.type === 'network' && error.isRetryable,
      recover: async (error) => {
        // Simulate network retry
        await new Promise(resolve => setTimeout(resolve, 1000));
        return Math.random() > 0.3; // 70% success rate
      },
      maxAttempts: 3
    });

    // Validation error recovery
    this.strategies.push({
      name: 'ValidationSanitize',
      canRecover: (error) => error.type === 'validation',
      recover: async (error) => {
        // Attempt to sanitize and retry
        return Math.random() > 0.5; // 50% success rate
      },
      maxAttempts: 1
    });
  }

  async recoverFromError(error: AppError): Promise<boolean> {
    const applicableStrategy = this.strategies.find(strategy => 
      strategy.canRecover(error)
    );

    if (!applicableStrategy) {
      return false;
    }

    let attempts = 0;
    let success = false;

    while (attempts < applicableStrategy.maxAttempts && !success) {
      attempts++;
      
      try {
        success = await applicableStrategy.recover(error);
      } catch (recoveryError) {
        console.error('Recovery attempt failed:', recoveryError);
      }
    }

    // Record recovery attempt
    const recoveryAttempt: RecoveryAttempt = {
      error,
      strategy: applicableStrategy.name,
      attempts,
      success,
      timestamp: new Date()
    };

    this.recoveryHistory.push(recoveryAttempt);
    this.updateStats(success);

    return success;
  }

  private updateStats(success: boolean): void {
    this.stats.totalRecoveries++;
    
    if (success) {
      this.stats.successfulRecoveries++;
    } else {
      this.stats.failedRecoveries++;
    }

    this.stats.successRate = this.stats.totalRecoveries > 0 
      ? (this.stats.successfulRecoveries / this.stats.totalRecoveries) * 100 
      : 0;
    
    this.stats.lastRecovery = new Date();
  }

  getRecoveryStats(): RecoveryStats {
    return { ...this.stats };
  }

  getRecoveryHistory(limit: number = 10): RecoveryAttempt[] {
    return this.recoveryHistory.slice(-limit);
  }

  addRecoveryStrategy(strategy: RecoveryStrategy): void {
    this.strategies.push(strategy);
  }

  clearHistory(): void {
    this.recoveryHistory = [];
    this.stats = {
      totalRecoveries: 0,
      successfulRecoveries: 0,
      failedRecoveries: 0,
      successRate: 0
    };
  }
}

// Utility function
export async function recoverFromError(error: AppError): Promise<boolean> {
  return await errorRecoveryService.recoverFromError(error);
}

// Singleton instance
export const errorRecoveryService = new ErrorRecoveryService();
